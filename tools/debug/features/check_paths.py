#!/usr/bin/env python3
"""
Simple script to check if the projection data paths exist and list their contents
"""

import os

def check_paths():
    """Check if the projection data paths exist"""
    projection_dirs = [
        "/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0722_256x256/",
        "/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0731_512x512/",
        "/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0804_1024x1024/",
    ]
    
    print("🔍 Checking projection data paths:")
    print("=" * 60)
    
    for proj_dir in projection_dirs:
        print(f"\n📁 {proj_dir}")
        
        if not os.path.exists(proj_dir):
            print(f"❌ Directory does not exist")
            continue
        
        print(f"✅ Directory exists")
        
        # Check for train/val/test subdirectories
        for subset in ['train', 'val', 'test']:
            subset_dir = os.path.join(proj_dir, subset)
            if os.path.exists(subset_dir):
                try:
                    files = os.listdir(subset_dir)
                    image_files = [f for f in files if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
                    print(f"  📂 {subset}: {len(image_files)} images")
                    
                    # Show a few examples
                    for i, img in enumerate(image_files[:3]):
                        print(f"    • {img}")
                    if len(image_files) > 3:
                        print(f"    ... and {len(image_files) - 3} more")
                        
                except OSError as e:
                    print(f"  ❌ Error reading {subset_dir}: {e}")
            else:
                print(f"  📂 {subset}: Directory not found")
        
        # Also check if there are images directly in the main directory
        try:
            files = os.listdir(proj_dir)
            image_files = [f for f in files if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
            if image_files:
                print(f"  📂 main directory: {len(image_files)} images")
                for i, img in enumerate(image_files[:3]):
                    print(f"    • {img}")
                if len(image_files) > 3:
                    print(f"    ... and {len(image_files) - 3} more")
        except OSError as e:
            print(f"  ❌ Error reading main directory: {e}")

if __name__ == '__main__':
    check_paths()
