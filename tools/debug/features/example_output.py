#!/usr/bin/env python3
"""
Example Output Generator

This script creates mock visualizations to demonstrate what the feature map visualizer
output would look like when comparing networks trained on different resolutions.
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.gridspec import GridSpec
from pathlib import Path

def create_mock_feature_map(height, width, pattern_type='geometric'):
    """Create a mock feature map with different patterns"""
    feature_map = np.zeros((height, width))
    
    if pattern_type == 'geometric':
        # Create geometric patterns (simulating room boundaries)
        # Horizontal lines
        feature_map[height//4:height//4+2, :] = 1.0
        feature_map[3*height//4:3*height//4+2, :] = 1.0
        
        # Vertical lines
        feature_map[:, width//4:width//4+2] = 1.0
        feature_map[:, 3*width//4:3*width//4+2] = 1.0
        
        # Add some noise for realism
        noise = np.random.normal(0, 0.1, (height, width))
        feature_map += noise
        
    elif pattern_type == 'detailed':
        # More detailed patterns (simulating high-resolution features)
        for i in range(0, height, 8):
            feature_map[i:i+2, :] = 0.5
        for j in range(0, width, 8):
            feature_map[:, j:j+2] = 0.5
            
        # Add diagonal patterns
        for i in range(height):
            for j in range(width):
                if (i + j) % 16 < 4:
                    feature_map[i, j] = 0.8
                    
        # Add noise
        noise = np.random.normal(0, 0.05, (height, width))
        feature_map += noise
        
    elif pattern_type == 'blob':
        # Blob-like patterns
        center_y, center_x = height//2, width//2
        y, x = np.ogrid[:height, :width]
        mask = (x - center_x)**2 + (y - center_y)**2 <= (min(height, width)//4)**2
        feature_map[mask] = 1.0
        
        # Add smaller blobs
        for _ in range(3):
            cy = np.random.randint(height//4, 3*height//4)
            cx = np.random.randint(width//4, 3*width//4)
            radius = np.random.randint(10, 20)
            y, x = np.ogrid[:height, :width]
            mask = (x - cx)**2 + (y - cy)**2 <= radius**2
            feature_map[mask] = np.random.uniform(0.5, 1.0)
    
    # Normalize to [0, 1]
    feature_map = np.clip(feature_map, 0, 1)
    return feature_map


def create_feature_comparison_demo():
    """Create a demo of feature map comparison"""
    print("🎨 Creating feature comparison demo...")
    
    # Simulate different resolution models
    models = {
        'Model 512x512': {'size': (64, 64), 'detail_level': 'basic'},
        'Model 1024x1024': {'size': (128, 128), 'detail_level': 'detailed'}
    }
    
    n_channels = 8
    n_models = len(models)
    
    # Create figure
    fig = plt.figure(figsize=(4 * n_channels, 6 * n_models))
    gs = GridSpec(n_models, n_channels, figure=fig, hspace=0.3, wspace=0.1)
    
    fig.suptitle('Feature Map Comparison: Different Input Resolutions\nImage: cuishanlantian_res_ff_RS10_11', 
                fontsize=16, fontweight='bold')
    
    for model_idx, (model_name, model_info) in enumerate(models.items()):
        height, width = model_info['size']
        detail_level = model_info['detail_level']
        
        for ch_idx in range(n_channels):
            ax = fig.add_subplot(gs[model_idx, ch_idx])
            
            # Create different patterns for different channels
            if ch_idx % 3 == 0:
                pattern = 'geometric'
            elif ch_idx % 3 == 1:
                pattern = 'detailed' if detail_level == 'detailed' else 'geometric'
            else:
                pattern = 'blob'
            
            feature_map = create_mock_feature_map(height, width, pattern)
            
            # Display feature map
            im = ax.imshow(feature_map, cmap='viridis', aspect='auto')
            ax.set_title(f'{model_name}\nChannel {ch_idx}', fontsize=10)
            ax.axis('off')
            
            # Add colorbar for the first channel of each model
            if ch_idx == 0:
                plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
    
    # Save demo
    output_dir = Path("./demo_output")
    output_dir.mkdir(exist_ok=True)
    output_path = output_dir / "feature_comparison_demo.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"💾 Feature comparison demo saved: {output_path}")
    return str(output_path)


def create_colorized_demo():
    """Create a demo of colorized feature maps"""
    print("🌈 Creating colorized feature maps demo...")
    
    models = {
        'Model 512x512': (64, 64),
        'Model 1024x1024': (128, 128)
    }
    
    fig, axes = plt.subplots(1, len(models), figsize=(6 * len(models), 6))
    if len(models) == 1:
        axes = [axes]
    
    fig.suptitle('Colorized Feature Maps (PCA-based)\nImage: cuishanlantian_res_ff_RS10_11', 
                fontsize=16, fontweight='bold')
    
    for idx, (model_name, (height, width)) in enumerate(models.items()):
        # Create a colorized feature map
        # Simulate PCA-based coloring by creating RGB channels
        r_channel = create_mock_feature_map(height, width, 'geometric')
        g_channel = create_mock_feature_map(height, width, 'detailed')
        b_channel = create_mock_feature_map(height, width, 'blob')
        
        # Combine into RGB image
        colored_image = np.stack([r_channel, g_channel, b_channel], axis=2)
        
        # Display
        axes[idx].imshow(colored_image)
        axes[idx].set_title(f'{model_name}\n192 channels, {height}×{width}', fontsize=12)
        axes[idx].axis('off')
    
    # Save demo
    output_dir = Path("./demo_output")
    output_dir.mkdir(exist_ok=True)
    output_path = output_dir / "colorized_features_demo.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"💾 Colorized demo saved: {output_path}")
    return str(output_path)


def create_analysis_summary():
    """Create a summary of what the analysis would show"""
    print("\n📊 Analysis Summary:")
    print("=" * 60)
    print("Expected differences between 512x512 and 1024x1024 models:")
    print()
    print("🔍 Spatial Resolution:")
    print("  • 512x512 model: Feature maps at ~64×64 resolution")
    print("  • 1024x1024 model: Feature maps at ~128×128 resolution")
    print("  • Higher resolution preserves finer spatial details")
    print()
    print("🏗️  Geometric Detail Capture:")
    print("  • Low-res: Captures major room boundaries and large structures")
    print("  • High-res: Captures door frames, window details, wall textures")
    print("  • High-res shows better edge definition and corner detection")
    print()
    print("🎨 Feature Diversity:")
    print("  • High-res models learn more diverse feature patterns")
    print("  • Better separation of different architectural elements")
    print("  • More specialized filters for fine-grained details")
    print()
    print("📈 Research Implications:")
    print("  • Validates the importance of high-resolution training data")
    print("  • Shows network's ability to adapt to input resolution")
    print("  • Demonstrates improved geometric understanding")


def main():
    """Main demo function"""
    print("🎨 Feature Map Visualizer - Example Output Generator")
    print("=" * 60)
    
    try:
        # Create demo visualizations
        feature_path = create_feature_comparison_demo()
        colorized_path = create_colorized_demo()
        
        # Show analysis summary
        create_analysis_summary()
        
        print(f"\n🎉 Demo visualizations created!")
        print(f"📁 Output directory: ./demo_output/")
        print(f"📊 Feature comparison: {Path(feature_path).name}")
        print(f"🌈 Colorized features: {Path(colorized_path).name}")
        
        print(f"\n💡 These demos show what the actual feature visualizer would produce")
        print(f"   when comparing real models trained on different resolutions.")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error creating demo: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    exit(main())
