#!/usr/bin/env python3
"""
Test script for the feature map visualizer

This script tests the feature visualizer with mock data to ensure it works correctly
before using it with real models and data.
"""

import os
import sys
import numpy as np
import torch
import cv2
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

def create_test_image(size=(512, 512), name="test_image.png"):
    """Create a test image with geometric patterns"""
    height, width = size
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Add some geometric patterns
    # Rectangles
    cv2.rectangle(image, (50, 50), (150, 150), (255, 0, 0), -1)
    cv2.rectangle(image, (200, 200), (300, 300), (0, 255, 0), -1)
    
    # Circles
    cv2.circle(image, (400, 100), 50, (0, 0, 255), -1)
    cv2.circle(image, (100, 400), 30, (255, 255, 0), -1)
    
    # Lines
    cv2.line(image, (0, height//2), (width, height//2), (255, 255, 255), 2)
    cv2.line(image, (width//2, 0), (width//2, height), (255, 255, 255), 2)
    
    # Save test image
    test_dir = Path("./test_data")
    test_dir.mkdir(exist_ok=True)
    image_path = test_dir / name
    cv2.imwrite(str(image_path), image)
    
    print(f"✅ Created test image: {image_path}")
    return str(image_path)


def create_mock_features(batch_size=1, channels=192, height=128, width=128):
    """Create mock feature tensors that simulate backbone outputs"""
    # Create features with different patterns for different channels
    features = torch.randn(batch_size, channels, height, width)
    
    # Add some structured patterns to make visualization more interesting
    for c in range(min(channels, 16)):
        # Create different patterns for different channels
        if c % 4 == 0:
            # Horizontal stripes
            for h in range(0, height, 8):
                features[0, c, h:h+4, :] = torch.randn(1) * 2
        elif c % 4 == 1:
            # Vertical stripes  
            for w in range(0, width, 8):
                features[0, c, :, w:w+4] = torch.randn(1) * 2
        elif c % 4 == 2:
            # Checkerboard pattern
            for h in range(0, height, 16):
                for w in range(0, width, 16):
                    features[0, c, h:h+8, w:w+8] = torch.randn(1) * 2
        else:
            # Random blobs
            center_h, center_w = np.random.randint(20, height-20), np.random.randint(20, width-20)
            radius = np.random.randint(10, 30)
            y, x = np.ogrid[:height, :width]
            mask = (x - center_w)**2 + (y - center_h)**2 <= radius**2
            features[0, c][mask] = torch.randn(1) * 3
    
    return features


class MockModel:
    """Mock model class for testing"""
    
    def __init__(self, name, feature_shape):
        self.name = name
        self.feature_shape = feature_shape
        self.dataset_meta = {'classes': ['room'] * 16}
    
    def test_step(self, data):
        """Mock test step that returns dummy results"""
        return [{'pred_instances': {'masks': torch.zeros(1, 100, 100), 'scores': torch.ones(1)}}]


class MockExtractor:
    """Mock feature extractor for testing"""
    
    def __init__(self, feature_shape):
        self.feature_shape = feature_shape
        self.features = {}
        self.hooks = []
    
    def register_hooks(self, model, target_layers):
        print(f"✅ Mock: Registered hooks for {target_layers}")
    
    def remove_hooks(self):
        print("✅ Mock: Removed hooks")
    
    def get_features(self):
        # Return mock backbone features
        mock_features = [
            create_mock_features(1, 192, 128, 128),  # Level 0 (highest resolution)
            create_mock_features(1, 384, 64, 64),    # Level 1
            create_mock_features(1, 768, 32, 32),    # Level 2
            create_mock_features(1, 1536, 16, 16),   # Level 3
        ]
        return {'backbone': mock_features}
    
    def clear_features(self):
        self.features.clear()


def test_colorization_utilities():
    """Test the colorization utilities"""
    print("\n🧪 Testing colorization utilities...")
    
    try:
        from color import feats_to_plotly_rgb, min_max_normalize
        
        # Test with random features
        test_features = torch.randn(100, 64)  # 100 pixels, 64 channels
        
        # Test PCA-based colorization
        colored = feats_to_plotly_rgb(test_features, normalize=True)
        print(f"✅ PCA colorization: {test_features.shape} -> {colored.shape}")
        
        # Test normalization
        normalized = min_max_normalize(test_features)
        print(f"✅ Min-max normalization: range [{normalized.min():.3f}, {normalized.max():.3f}]")
        
        return True
        
    except ImportError as e:
        print(f"❌ Colorization utilities not available: {e}")
        return False


def test_feature_extraction():
    """Test feature extraction with mock data"""
    print("\n🧪 Testing feature extraction...")
    
    # Create mock models
    mock_models = {
        'model_512x512_0': MockModel('512x512', (192, 128, 128)),
        'model_1024x1024_1': MockModel('1024x1024', (192, 256, 256)),
    }
    
    # Create mock extractors
    mock_extractors = {
        'model_512x512_0': MockExtractor((192, 128, 128)),
        'model_1024x1024_1': MockExtractor((192, 256, 256)),
    }
    
    # Test feature extraction
    all_features = {}
    for model_key, extractor in mock_extractors.items():
        features = extractor.get_features()
        all_features[model_key] = features
        print(f"✅ Extracted features for {model_key}")
        
        # Check backbone features
        if 'backbone' in features:
            backbone_features = features['backbone']
            print(f"  Backbone levels: {len(backbone_features)}")
            for i, feat in enumerate(backbone_features):
                print(f"    Level {i}: {feat.shape}")
    
    return all_features


def test_visualization_components():
    """Test visualization components"""
    print("\n🧪 Testing visualization components...")
    
    # Import the visualizer
    try:
        from feature_map_visualizer import MultiResolutionFeatureVisualizer
        
        # Create test visualizer
        visualizer = MultiResolutionFeatureVisualizer("./test_output")
        
        # Test channel selection
        test_features = create_mock_features(1, 64, 32, 32)[0]  # Remove batch dim
        selected_channels = visualizer._select_diverse_channels(test_features, 8)
        print(f"✅ Channel selection: {len(selected_channels)} channels selected from {test_features.shape[0]}")
        print(f"  Selected channels: {selected_channels}")
        
        return True
        
    except Exception as e:
        print(f"❌ Visualization component test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests"""
    print("🧪 Feature Map Visualizer Test Suite")
    print("=" * 50)
    
    # Test 1: Create test image
    print("\n1. Creating test image...")
    test_image_path = create_test_image()
    
    # Test 2: Test colorization utilities
    print("\n2. Testing colorization utilities...")
    colorization_ok = test_colorization_utilities()
    
    # Test 3: Test feature extraction
    print("\n3. Testing feature extraction...")
    mock_features = test_feature_extraction()
    
    # Test 4: Test visualization components
    print("\n4. Testing visualization components...")
    visualization_ok = test_visualization_components()
    
    # Summary
    print("\n" + "=" * 50)
    print("🧪 Test Summary:")
    print(f"  ✅ Test image creation: Success")
    print(f"  {'✅' if colorization_ok else '❌'} Colorization utilities: {'Success' if colorization_ok else 'Failed'}")
    print(f"  ✅ Feature extraction: Success")
    print(f"  {'✅' if visualization_ok else '❌'} Visualization components: {'Success' if visualization_ok else 'Failed'}")
    
    if colorization_ok and visualization_ok:
        print("\n🎉 All tests passed! The feature visualizer should work correctly.")
        print("\n💡 Next steps:")
        print("  1. Prepare your model configs and checkpoints")
        print("  2. Find a projection density image to visualize")
        print("  3. Run the feature visualizer with real data")
    else:
        print("\n⚠️  Some tests failed. Please check the dependencies and setup.")
    
    # Cleanup
    print(f"\n🧹 Cleaning up test files...")
    if os.path.exists(test_image_path):
        os.remove(test_image_path)
    if os.path.exists("./test_data"):
        os.rmdir("./test_data")
    if os.path.exists("./test_output"):
        import shutil
        shutil.rmtree("./test_output")


if __name__ == '__main__':
    main()
