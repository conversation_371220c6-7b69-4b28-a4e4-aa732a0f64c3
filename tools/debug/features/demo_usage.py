#!/usr/bin/env python3
"""
Demo Usage Script for Feature Map Visualizer

This script demonstrates how to use the feature map visualizer with real model configs
and checkpoints to compare feature maps from networks trained on different resolutions.

Before running this script, ensure you have:
1. Installed the required dependencies (see README.md)
2. Trained models with different input resolutions
3. A projection density image to visualize

This script provides example commands and validates the setup.
"""

import os
import sys
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are available"""
    print("🔍 Checking dependencies...")
    
    missing_deps = []
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
    except ImportError:
        missing_deps.append("torch")
        print("❌ PyTorch not found")
    
    try:
        import mmdet
        print(f"✅ MMDetection: {mmdet.__version__}")
    except ImportError:
        missing_deps.append("mmdet")
        print("❌ MMDetection not found")
    
    try:
        import cv2
        print(f"✅ OpenCV: {cv2.__version__}")
    except ImportError:
        missing_deps.append("opencv-python")
        print("❌ OpenCV not found")
    
    try:
        import matplotlib
        print(f"✅ Matplotlib: {matplotlib.__version__}")
    except ImportError:
        missing_deps.append("matplotlib")
        print("❌ Matplotlib not found")
    
    try:
        import numpy as np
        print(f"✅ NumPy: {np.__version__}")
    except ImportError:
        missing_deps.append("numpy")
        print("❌ NumPy not found")
    
    # Optional dependencies
    try:
        from colorhash import ColorHash
        print("✅ ColorHash available")
    except ImportError:
        print("⚠️  ColorHash not found (optional for advanced coloring)")
    
    try:
        from plotly.colors import sample_colorscale
        print("✅ Plotly available")
    except ImportError:
        print("⚠️  Plotly not found (optional for advanced coloring)")
    
    return missing_deps


def find_example_configs():
    """Find example model configurations in the workspace"""
    print("\n🔍 Looking for example model configurations...")
    
    # Look for work_dirs with different resolutions
    work_dirs = Path("../../../work_dirs")
    if not work_dirs.exists():
        print("❌ work_dirs not found. Please ensure you have trained models.")
        return []
    
    configs = []
    for dir_path in work_dirs.iterdir():
        if dir_path.is_dir():
            config_file = dir_path / "mask2former_config.py"
            if config_file.exists():
                configs.append(str(config_file))
                print(f"✅ Found config: {config_file}")
    
    return configs


def find_example_checkpoints():
    """Find example model checkpoints"""
    print("\n🔍 Looking for example model checkpoints...")
    
    work_dirs = Path("../../../work_dirs")
    if not work_dirs.exists():
        return []
    
    checkpoints = []
    for dir_path in work_dirs.iterdir():
        if dir_path.is_dir():
            # Look for best checkpoint files
            for pattern in ["best_*.pth", "latest.pth", "epoch_*.pth"]:
                checkpoint_files = list(dir_path.glob(pattern))
                if checkpoint_files:
                    # Take the first match
                    checkpoints.append(str(checkpoint_files[0]))
                    print(f"✅ Found checkpoint: {checkpoint_files[0]}")
                    break
    
    return checkpoints


def generate_example_commands(configs, checkpoints):
    """Generate example commands for running the feature visualizer"""
    print("\n📝 Example Commands:")
    print("=" * 60)
    
    if len(configs) >= 2 and len(checkpoints) >= 2:
        print("# Compare two models with different resolutions:")
        print("python feature_map_visualizer.py \\")
        print(f"    --configs {configs[0]} \\")
        print(f"              {configs[1]} \\")
        print(f"    --checkpoints {checkpoints[0]} \\")
        print(f"                  {checkpoints[1]} \\")
        print("    --image-name \"cuishanlantian_res_ff_RS10_11\" \\")
        print("    --output-dir ./feature_visualizations")
        
        print("\n# Alternative with specific image path:")
        print("python feature_map_visualizer.py \\")
        print(f"    --configs {configs[0]} {configs[1]} \\")
        print(f"    --checkpoints {checkpoints[0]} {checkpoints[1]} \\")
        print("    --image-path /path/to/your/projection_image.png \\")
        print("    --output-dir ./feature_visualizations \\")
        print("    --max-channels 20")
        
    else:
        print("# Template command (replace with your actual paths):")
        print("python feature_map_visualizer.py \\")
        print("    --configs work_dirs/model_512x512/mask2former_config.py \\")
        print("              work_dirs/model_1024x1024/mask2former_config.py \\")
        print("    --checkpoints work_dirs/model_512x512/best_model.pth \\")
        print("                  work_dirs/model_1024x1024/best_model.pth \\")
        print("    --image-name \"cuishanlantian_res_ff_RS10_11\" \\")
        print("    --output-dir ./feature_visualizations")


def show_setup_instructions():
    """Show setup instructions"""
    print("\n🛠️  Setup Instructions:")
    print("=" * 60)
    print("1. Install dependencies:")
    print("   conda env create -f ../mask2former_env.yml")
    print("   conda activate mask2former")
    print()
    print("2. Ensure you have trained models with different resolutions:")
    print("   - Models should be in work_dirs/ with descriptive names")
    print("   - Each should have a mask2former_config.py and checkpoint file")
    print()
    print("3. Prepare a projection density image:")
    print("   - Use an image name like 'cuishanlantian_res_ff_RS10_11'")
    print("   - Or provide the full path with --image-path")
    print()
    print("4. Run the feature visualizer:")
    print("   python feature_map_visualizer.py [options]")


def main():
    """Main demo function"""
    print("🎨 Feature Map Visualizer - Demo & Setup Guide")
    print("=" * 60)
    
    # Check dependencies
    missing_deps = check_dependencies()
    
    if missing_deps:
        print(f"\n❌ Missing dependencies: {', '.join(missing_deps)}")
        show_setup_instructions()
        return 1
    
    print("\n✅ All required dependencies are available!")
    
    # Look for example configurations
    configs = find_example_configs()
    checkpoints = find_example_checkpoints()
    
    # Generate example commands
    generate_example_commands(configs, checkpoints)
    
    # Show expected results
    print("\n🎯 Expected Results:")
    print("=" * 60)
    print("The feature visualizer will generate:")
    print("1. Feature comparison visualization showing individual channels")
    print("2. Colorized feature maps using PCA-based coloring")
    print("3. Side-by-side comparison of different resolution models")
    print()
    print("Higher resolution models should show:")
    print("- More detailed spatial patterns")
    print("- Better preservation of geometric structures")
    print("- Richer feature representations at object boundaries")
    
    # Show next steps
    print("\n🚀 Next Steps:")
    print("=" * 60)
    if not missing_deps:
        if configs and checkpoints:
            print("✅ You have model configs and checkpoints available")
            print("✅ Ready to run feature visualization!")
            print("   Use the example commands above")
        else:
            print("⚠️  No trained models found in work_dirs/")
            print("   Please train models with different resolutions first")
    else:
        print("1. Set up the conda environment:")
        print("   conda env create -f ../mask2former_env.yml")
        print("   conda activate mask2former")
        print("2. Train models with different input resolutions")
        print("3. Run the feature visualizer")
    
    return 0


if __name__ == '__main__':
    exit(main())
