#!/usr/bin/env python3
"""
Simplified Feature Extractor for Testing

This script provides a minimal implementation to test feature extraction
without the complex visualization components.
"""

import os
import sys
import cv2
import numpy as np

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

def extract_features_simple():
    """Simple feature extraction test"""
    
    print("🧪 Simple Feature Extraction Test")
    print("=" * 50)
    
    # Import dependencies
    try:
        import torch
        print("✅ PyTorch imported")
    except ImportError as e:
        print(f"❌ PyTorch import failed: {e}")
        return False
    
    try:
        from mmdet.apis import init_detector, inference_detector
        print("✅ MMDetection imported")
    except ImportError as e:
        print(f"❌ MMDetection import failed: {e}")
        return False
    
    # Model paths
    config_path = "work_dirs/hc_rs10_q3_with_floor_2_0808_1024x1024/mask2former_config.py"
    checkpoint_path = "work_dirs/hc_rs10_q3_with_floor_2_0808_1024x1024/best_coco_segm_mAP_85_epoch_235.pth"
    
    print(f"\n🔧 Loading model...")
    try:
        # Handle custom imports issue
        from mmengine.config import Config
        cfg = Config.fromfile(config_path)
        
        if hasattr(cfg, 'custom_imports'):
            cfg.custom_imports = dict(imports=[], allow_failed_imports=True)
        
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            cfg.dump(f.name)
            temp_config = f.name
        
        try:
            model = init_detector(temp_config, checkpoint_path, device="cuda:0")
            print("✅ Model loaded successfully")
        finally:
            if os.path.exists(temp_config):
                os.unlink(temp_config)
                
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return False
    
    # Load test image
    image_path = "/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0722_256x256/train/cuishanlantian_res_ff_RS10_11.png"
    
    print(f"\n🖼️  Loading image: {os.path.basename(image_path)}")
    if not os.path.exists(image_path):
        print(f"❌ Image not found: {image_path}")
        return False
    
    image = cv2.imread(image_path)
    if image is None:
        print(f"❌ Failed to load image")
        return False
    
    print(f"📐 Image shape: {image.shape}")
    
    # Simple hook to capture backbone features
    backbone_features = {}
    
    def hook_fn(name):
        def hook(module, input, output):
            if isinstance(output, (list, tuple)):
                backbone_features[name] = [feat.detach().cpu() for feat in output]
            else:
                backbone_features[name] = output.detach().cpu()
        return hook
    
    # Register hook on backbone
    hooks = []
    for name, module in model.named_modules():
        if 'backbone' in name and len(list(module.children())) == 0:  # Leaf modules only
            hook = module.register_forward_hook(hook_fn(name))
            hooks.append(hook)
            print(f"📌 Registered hook: {name}")
            break  # Just register one hook for testing
    
    print(f"\n🔍 Running inference to extract features...")
    try:
        # Run inference (this will trigger hooks)
        with torch.no_grad():
            result = inference_detector(model, image_path)
        
        print(f"✅ Inference completed")
        print(f"📊 Captured {len(backbone_features)} feature sets")
        
        # Show feature information
        for name, features in backbone_features.items():
            if isinstance(features, list):
                print(f"  {name}: {len(features)} levels")
                for i, feat in enumerate(features):
                    print(f"    Level {i}: {feat.shape}")
            else:
                print(f"  {name}: {features.shape}")
        
        # Clean up hooks
        for hook in hooks:
            hook.remove()
        
        return True
        
    except Exception as e:
        print(f"❌ Feature extraction failed: {e}")
        import traceback
        traceback.print_exc()
        
        # Clean up hooks
        for hook in hooks:
            hook.remove()
        
        return False

def main():
    """Main function"""
    success = extract_features_simple()
    
    if success:
        print(f"\n🎉 Feature extraction test passed!")
        print(f"💡 The full feature visualizer should work now")
    else:
        print(f"\n❌ Feature extraction test failed")
        print(f"🔧 Please check the setup and dependencies")
    
    return 0 if success else 1

if __name__ == '__main__':
    exit(main())
