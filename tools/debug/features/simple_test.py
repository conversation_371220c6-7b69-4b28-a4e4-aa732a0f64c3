#!/usr/bin/env python3
"""
Simple test script to check paths and find images
"""

import os

def find_cuishanlantian_images():
    """Find cuishanlantian images in the available directories"""
    
    # Check both the specified paths and output directories
    search_dirs = [
        "/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0722_256x256/",
        "/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0731_512x512/",
        "/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0804_1024x1024/",
        "output/hc_rs10_q2_wo_floor_2_0722_256x256_test/",
        "output/hc_rs10_q2_wo_floor_2_0731_512x512_test/",
        "output/hc_rs10_q2_wo_floor_2_0804_1024x1024_epoch_1850_test/",
        "output/hc_rs10_q2_wo_floor_2_0804_1024x1024_epoch_1850_train/",
        "output/hc_rs10_q2_wo_floor_2_0804_1024x1024_epoch_1850_val/",
        "output/hc_rs10_q3_1024x1024_0808_v1_epoch_235_train/",
        "output/hc_rs10_q3_1024x1024_0808_v1_epoch_235_val/",
    ]
    
    target_name = "cuishanlantian_res_ff_RS10_11"
    extensions = ['.png', '.jpg', '.jpeg']
    
    print(f"🔍 Searching for '{target_name}' in available directories:")
    print("=" * 70)
    
    found_images = []
    
    for search_dir in search_dirs:
        print(f"\n📁 Checking: {search_dir}")
        
        if not os.path.exists(search_dir):
            print(f"  ❌ Directory does not exist")
            continue
        
        print(f"  ✅ Directory exists")
        
        # Search directly in the directory
        try:
            files = os.listdir(search_dir)
            matching_files = []
            
            for file in files:
                name_without_ext = os.path.splitext(file)[0]
                # Remove _result suffix if present
                if name_without_ext.endswith('_result'):
                    name_without_ext = name_without_ext[:-7]
                
                if any(file.lower().endswith(ext) for ext in extensions):
                    if target_name == name_without_ext:
                        full_path = os.path.join(search_dir, file)
                        matching_files.append(full_path)
                        found_images.append(full_path)
                        print(f"  🎯 EXACT MATCH: {file}")
                    elif target_name in name_without_ext:
                        print(f"  🔍 Partial match: {file}")
            
            if not matching_files:
                # Show some example files for reference
                image_files = [f for f in files if any(f.lower().endswith(ext) for ext in extensions)]
                if image_files:
                    print(f"  📋 Found {len(image_files)} images, examples:")
                    for i, img in enumerate(image_files[:3]):
                        print(f"    • {img}")
                    if len(image_files) > 3:
                        print(f"    ... and {len(image_files) - 3} more")
                else:
                    print(f"  📋 No image files found")
                    
        except OSError as e:
            print(f"  ❌ Error reading directory: {e}")
        
        # Also check subdirectories
        for subset in ['train', 'val', 'test']:
            subset_dir = os.path.join(search_dir, subset)
            if os.path.exists(subset_dir):
                print(f"  📂 Checking {subset} subdirectory...")
                try:
                    files = os.listdir(subset_dir)
                    for file in files:
                        name_without_ext = os.path.splitext(file)[0]
                        if name_without_ext.endswith('_result'):
                            name_without_ext = name_without_ext[:-7]
                        
                        if any(file.lower().endswith(ext) for ext in extensions):
                            if target_name == name_without_ext:
                                full_path = os.path.join(subset_dir, file)
                                found_images.append(full_path)
                                print(f"    🎯 EXACT MATCH in {subset}: {file}")
                            elif target_name in name_without_ext:
                                print(f"    🔍 Partial match in {subset}: {file}")
                except OSError as e:
                    print(f"    ❌ Error reading {subset_dir}: {e}")
    
    print(f"\n" + "=" * 70)
    print(f"📊 SUMMARY:")
    if found_images:
        print(f"✅ Found {len(found_images)} exact matches for '{target_name}':")
        for img in found_images:
            print(f"  • {img}")
    else:
        print(f"❌ No exact matches found for '{target_name}'")
        print(f"💡 Try using --list-images to see all available images")
        print(f"💡 Or use a partial name that matches the available files")
    
    return found_images

if __name__ == '__main__':
    find_cuishanlantian_images()
