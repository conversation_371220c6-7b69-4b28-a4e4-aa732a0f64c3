#!/usr/bin/env python3
"""
Simple test script to list available images in projection directories
"""

import os
from pathlib import Path

def list_images_simple():
    """Simple function to list images"""
    projection_dirs = [
        "/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0804_1024x1024/",
        "/home/<USER>/data/RS10_data/hc_rs10_q3_with_floor_2_0808_1024x1024/",
        "/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0804_512x512/",
        "/home/<USER>/data/RS10_data/hc_rs10_q3_with_floor_2_0808_512x512/",
    ]
    
    extensions = ['.png', '.jpg', '.jpeg']
    
    print("📋 Available images in projection directories:")
    print("=" * 60)
    
    for proj_dir in projection_dirs:
        if not os.path.exists(proj_dir):
            print(f"⚠️  Directory not found: {proj_dir}")
            continue
        
        print(f"\n📁 {proj_dir}")
        
        for subset in ['train', 'val', 'test']:
            subset_dir = os.path.join(proj_dir, subset)
            if not os.path.exists(subset_dir):
                print(f"  📂 {subset}: Directory not found")
                continue
            
            try:
                files = os.listdir(subset_dir)
                image_files = []
                
                for file in files:
                    if any(file.lower().endswith(ext) for ext in extensions):
                        name_without_ext = os.path.splitext(file)[0]
                        image_files.append(name_without_ext)
                
                if image_files:
                    print(f"  📂 {subset}: {len(image_files)} images")
                    # Show first few examples
                    for i, img in enumerate(sorted(image_files)[:5]):
                        print(f"    • {img}")
                    if len(image_files) > 5:
                        print(f"    ... and {len(image_files) - 5} more")
                else:
                    print(f"  📂 {subset}: No images found")
                    
            except OSError as e:
                print(f"  ❌ Error reading {subset_dir}: {e}")

if __name__ == '__main__':
    list_images_simple()
