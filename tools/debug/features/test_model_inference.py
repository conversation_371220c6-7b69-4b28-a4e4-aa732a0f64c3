#!/usr/bin/env python3
"""
Simple test script to verify model loading and basic inference
"""

import os
import sys
import cv2
import torch
import numpy as np

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

def test_model_loading():
    """Test basic model loading and inference"""
    
    try:
        from mmdet.apis import init_detector
        print("✅ MMDetection imported successfully")
    except ImportError as e:
        print(f"❌ MMDetection import failed: {e}")
        return False
    
    # Model paths
    config_path = "work_dirs/hc_rs10_q3_with_floor_2_0808_1024x1024/mask2former_config.py"
    checkpoint_path = "work_dirs/hc_rs10_q3_with_floor_2_0808_1024x1024/best_coco_segm_mAP_85_epoch_235.pth"
    
    if not os.path.exists(config_path):
        print(f"❌ Config not found: {config_path}")
        return False
    
    if not os.path.exists(checkpoint_path):
        print(f"❌ Checkpoint not found: {checkpoint_path}")
        return False
    
    print(f"🔧 Loading model...")
    print(f"  Config: {config_path}")
    print(f"  Checkpoint: {checkpoint_path}")
    
    try:
        # Try to load model with custom imports disabled
        from mmengine.config import Config
        cfg = Config.fromfile(config_path)
        
        # Temporarily disable custom imports
        if hasattr(cfg, 'custom_imports'):
            print(f"  📝 Disabling custom_imports temporarily...")
            cfg.custom_imports = dict(imports=[], allow_failed_imports=True)
        
        # Save temporary config
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            cfg.dump(f.name)
            temp_config = f.name
        
        try:
            model = init_detector(temp_config, checkpoint_path, device="cuda:0")
            print(f"✅ Model loaded successfully")
        finally:
            # Clean up temp file
            if os.path.exists(temp_config):
                os.unlink(temp_config)
        
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return False
    
    # Test image loading
    image_path = "/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0722_256x256/train/cuishanlantian_res_ff_RS10_11.png"
    
    if not os.path.exists(image_path):
        print(f"❌ Test image not found: {image_path}")
        return False
    
    print(f"🖼️  Loading test image: {os.path.basename(image_path)}")
    image = cv2.imread(image_path)
    if image is None:
        print(f"❌ Failed to load image")
        return False
    
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    print(f"📐 Image shape: {image_rgb.shape}")
    
    # Test basic inference
    print(f"🔍 Testing basic inference...")
    try:
        # Use the standard MMDetection inference API
        from mmdet.apis import inference_detector
        
        result = inference_detector(model, image_path)
        print(f"✅ Basic inference successful")
        print(f"📊 Result type: {type(result)}")
        
        if hasattr(result, 'pred_instances'):
            print(f"📊 Predictions: {len(result.pred_instances)} instances")
        
        return True
        
    except Exception as e:
        print(f"❌ Inference failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 Model Loading and Inference Test")
    print("=" * 50)
    
    success = test_model_loading()
    
    if success:
        print(f"\n🎉 All tests passed!")
        print(f"✅ Model loading works")
        print(f"✅ Basic inference works")
        print(f"💡 Ready to proceed with feature extraction")
    else:
        print(f"\n❌ Tests failed")
        print(f"🔧 Please check the model and data paths")
    
    return 0 if success else 1

if __name__ == '__main__':
    exit(main())
