# 特征图可视化工具 - 完整使用指南

## 🎯 目标
创建一个Python脚本来可视化不同分辨率训练的网络特征图，展示高分辨率输入如何使网络学习到更丰富的几何细节。

## 📁 已创建的文件

### 核心文件
- **`feature_map_visualizer.py`** - 主要可视化脚本（800+行，完全修正）
- **`simple_feature_extractor.py`** - 简化测试版本
- **`test_model_inference.py`** - 模型加载测试

### 文档和辅助文件
- **`README.md`** - 详细使用文档
- **`SOLUTION_SUMMARY.md`** - 技术解决方案总结
- **`run_feature_visualization.py`** - 设置助手
- **`demo_usage.py`** - 演示脚本
- **`example_output.py`** - 示例输出生成器

## 🚀 使用方法

### 环境准备
```bash
# 1. 激活conda环境
conda activate mask2former

# 2. 设置PYTHONPATH
export PYTHONPATH=/home/<USER>/repos/Mask2Former_v2:$PYTHONPATH

# 3. 验证环境
python -c "import torch; import mmdet; print('✅ 环境OK')"
```

### 基本使用

#### 单模型特征可视化
```bash
PYTHONPATH=/home/<USER>/repos/Mask2Former_v2 \
python tools/debug/features/feature_map_visualizer.py \
    --configs work_dirs/hc_rs10_q3_with_floor_2_0808_1024x1024/mask2former_config.py \
    --checkpoints work_dirs/hc_rs10_q3_with_floor_2_0808_1024x1024/best_coco_segm_mAP_85_epoch_235.pth \
    --image-name "cuishanlantian_res_ff_RS10_11" \
    --output-dir output/feature_visualizations
```

#### 多分辨率对比（推荐）
```bash
PYTHONPATH=/home/<USER>/repos/Mask2Former_v2 \
python tools/debug/features/feature_map_visualizer.py \
    --configs work_dirs/hc_rs10_q3_with_floor_2_0808_512x512/mask2former_config.py \
              work_dirs/hc_rs10_q3_with_floor_2_0808_1024x1024/mask2former_config.py \
    --checkpoints work_dirs/hc_rs10_q3_with_floor_2_0808_512x512/best_coco_segm_mAP_85_epoch_600.pth \
                  work_dirs/hc_rs10_q3_with_floor_2_0808_1024x1024/best_coco_segm_mAP_85_epoch_235.pth \
    --image-name "cuishanlantian_res_ff_RS10_11" \
    --output-dir output/feature_visualizations
```

### 高级选项
```bash
# 查看所有可用图像
python tools/debug/features/feature_map_visualizer.py --list-images

# 指定自定义投影目录
python tools/debug/features/feature_map_visualizer.py \
    --configs config1.py config2.py \
    --checkpoints checkpoint1.pth checkpoint2.pth \
    --image-name "cuishanlantian_res_ff_RS10_11" \
    --projection-dirs /custom/path1/ /custom/path2/ \
    --max-channels 20 \
    --device cuda:1
```

## 📊 数据路径说明

### 输入数据（密度图）
脚本会在以下路径中搜索原始密度图：
- `/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0722_256x256/`
- `/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0731_512x512/`
- `/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0804_1024x1024/`

### 模型文件
- 配置文件：`work_dirs/*/mask2former_config.py`
- 检查点：`work_dirs/*/best_*.pth`

## 🎨 输出结果

### 生成的可视化文件
1. **`{image_name}_feature_comparison.png`**
   - 并排显示不同模型的个别特征通道
   - 展示最高分辨率特征图（backbone stride 4）
   - 智能选择多样化的激活通道

2. **`{image_name}_colorized_features.png`**
   - 基于PCA的RGB彩色化特征图
   - 每个像素颜色代表该位置的特征向量
   - 可视化空间特征模式

### 预期研究结果
- **空间分辨率差异**：1024×1024模型产生~256×256特征图 vs 512×512模型的~128×128
- **几何细节捕获**：高分辨率模型捕获门框、窗户细节、墙面纹理
- **特征多样性**：更专业化的滤波器和更好的建筑元素分离

## 🔧 技术实现

### 特征提取策略
```python
# 使用PyTorch hooks捕获backbone输出
def hook_fn(name):
    def hook(module, input, output):
        if isinstance(output, (list, tuple)):
            # 多级backbone输出
            self.features[name] = [feat.detach().cpu() for feat in output]
        else:
            self.features[name] = output.detach().cpu()
    return hook
```

### 最高分辨率选择
- Backbone输出4个级别：[192, 384, 768, 1536]通道
- 步长：[4, 8, 16, 32] - 级别0（步长4）具有最高分辨率
- 自动选择级别0特征进行可视化

### 通道选择算法
- 分析激活统计（均值、标准差、稀疏性）
- 选择具有不同激活模式的多样化通道
- 避免冗余或不活跃的通道

## 🔍 故障排除

### 常见问题

1. **模型加载失败**
   ```
   ImportError: Failed to import tools.custom_metrics
   ```
   **解决方案**：脚本会自动禁用custom_imports并重试

2. **图像未找到**
   ```
   ❌ Image 'cuishanlantian_res_ff_RS10_11' not found
   ```
   **解决方案**：使用`--list-images`查看可用图像，或使用`--projection-dirs`指定路径

3. **CUDA内存不足**
   **解决方案**：使用`--device cpu`或减少比较的模型数量

4. **Python环境问题**
   **解决方案**：确保在正确的conda环境中，设置PYTHONPATH

### 调试步骤
1. 运行简化测试：`python tools/debug/features/simple_feature_extractor.py`
2. 检查模型加载：`python tools/debug/features/test_model_inference.py`
3. 验证图像路径：`python tools/debug/features/feature_map_visualizer.py --list-images`

## 🎉 研究价值

这个工具能够：
1. **验证分辨率优势**：直观展示高分辨率训练改善特征学习
2. **比较模型架构**：分析不同模型如何捕获几何细节
3. **理解特征层次**：检查特征如何在分辨率级别间演化
4. **生成发表图表**：创建高质量的研究可视化

## 📈 下一步

1. **运行设置助手**：`python tools/debug/features/run_feature_visualization.py`
2. **检查可用图像**：使用`--list-images`标志
3. **执行特征可视化**：使用生成的命令
4. **分析结果**：比较特征图验证分辨率优势
5. **生成图表**：创建发表质量的可视化

**特征可视化工具已完全准备就绪！** 它提供了一个强大的研究工具来展示高分辨率训练在几何细节学习中的优势。
