# 特征可视化工具 - 故障排除指南

## 🚨 常见问题及解决方案

### 1. Python环境问题

#### 问题：脚本启动时卡住或无输出
```bash
# 症状：运行命令后没有任何输出
PYTHONPATH=/home/<USER>/repos/Mask2Former_v2 python tools/debug/features/feature_map_visualizer.py ...
```

**解决方案：**
```bash
# 1. 确认conda环境
conda activate mask2former  # 或您的MMDetection环境

# 2. 验证依赖项
python -c "import torch; print('PyTorch:', torch.__version__)"
python -c "import mmdet; print('MMDetection:', mmdet.__version__)"
python -c "import mmcv; print('MMCV:', mmcv.__version__)"

# 3. 检查CUDA
python -c "import torch; print('CUDA available:', torch.cuda.is_available())"

# 4. 测试基本导入
python -c "from mmdet.apis import init_detector; print('MMDet APIs OK')"
```

### 2. 模型加载问题

#### 问题：custom_imports错误
```
ImportError: Failed to import tools.custom_metrics.coco_metric_iou85
```

**解决方案：**
脚本已自动处理此问题，但如果仍有问题：
```bash
# 确保项目根目录在PYTHONPATH中
export PYTHONPATH=/home/<USER>/repos/Mask2Former_v2:$PYTHONPATH

# 或者在脚本中手动禁用custom_imports
# 脚本已包含此逻辑
```

### 3. 数据路径问题

#### 问题：注释文件未找到
```
❌ Annotation file not found: /path/to/annotations.json
```

**解决方案：**
```bash
# 检查数据结构
ls -la /home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0804_1024x1024/
ls -la /home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0804_1024x1024/annotations/

# 确认注释文件存在
ls -la /home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0804_1024x1024/annotations/train.json
```

#### 问题：图像未找到
```
❌ Image 'cuishanlantian_res_ff_RS10_11' not found in COCO annotations
```

**解决方案：**
```bash
# 检查图像文件
find /home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0804_1024x1024/ -name "*cuishanlantian_res_ff_RS10_11*"

# 验证COCO注释
python -c "
from pycocotools.coco import COCO
coco = COCO('/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0804_1024x1024/annotations/train.json')
for img_id in coco.getImgIds()[:5]:
    img_info = coco.loadImgs(img_id)[0]
    print(f'Image: {img_info[\"file_name\"]}')
"
```

### 4. 特征提取问题

#### 问题：Hook注册失败
```
⚠️ No hooks registered for targets: ['backbone.stages.0']
```

**解决方案：**
```bash
# 检查模型结构
python -c "
from mmdet.apis import init_detector
model = init_detector('config.py', 'checkpoint.pth')
for name, module in model.named_modules():
    if 'backbone' in name and 'stages' in name:
        print(f'Available: {name}')
"
```

#### 问题：特征形状错误
```
AttributeError: 'tuple' object has no attribute 'detach'
```

**解决方案：**
脚本已修复此问题，包含混合输出处理逻辑。

### 5. 内存问题

#### 问题：CUDA内存不足
```
RuntimeError: CUDA out of memory
```

**解决方案：**
```bash
# 使用CPU推理
python tools/debug/features/feature_map_visualizer.py \
    --configs ... \
    --checkpoints ... \
    --image-name "cuishanlantian_res_ff_RS10_11" \
    --data-root /home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0804_1024x1024 \
    --device cpu \
    --output-dir output/feature_visualizations

# 或减少比较的模型数量
# 只使用一个模型进行测试
```

## 🔧 调试步骤

### 步骤1：环境验证
```bash
# 运行环境测试
python tools/debug/features/test_stage0_extraction.py
```

### 步骤2：模型加载测试
```bash
# 测试模型加载
python -c "
from mmdet.apis import init_detector
model = init_detector(
    'work_dirs/hc_rs10_q3_with_floor_2_0808_1024x1024/mask2former_config.py',
    'work_dirs/hc_rs10_q3_with_floor_2_0808_1024x1024/best_coco_segm_mAP_85_epoch_235.pth'
)
print('Model loaded successfully')
"
```

### 步骤3：数据加载测试
```bash
# 测试COCO数据加载
python -c "
from pycocotools.coco import COCO
coco = COCO('/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0804_1024x1024/annotations/train.json')
print(f'Loaded {len(coco.getImgIds())} images')
"
```

### 步骤4：推理测试
```bash
# 测试基本推理
python -c "
from mmdet.apis import init_detector, inference_detector
model = init_detector('config.py', 'checkpoint.pth')
result = inference_detector(model, '/path/to/image.png')
print('Inference successful')
"
```

## 📋 完整的工作命令

### 确保环境正确
```bash
# 1. 激活环境
conda activate mask2former

# 2. 设置路径
export PYTHONPATH=/home/<USER>/repos/Mask2Former_v2:$PYTHONPATH

# 3. 验证路径
echo $PYTHONPATH
```

### 运行特征可视化
```bash
# 单模型测试
PYTHONPATH=/home/<USER>/repos/Mask2Former_v2 \
python tools/debug/features/feature_map_visualizer.py \
    --configs work_dirs/hc_rs10_q3_with_floor_2_0808_1024x1024/mask2former_config.py \
    --checkpoints work_dirs/hc_rs10_q3_with_floor_2_0808_1024x1024/best_coco_segm_mAP_85_epoch_235.pth \
    --image-name "cuishanlantian_res_ff_RS10_11" \
    --data-root /home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0804_1024x1024 \
    --output-dir output/feature_visualizations \
    --max-channels 8
```

### 多分辨率对比
```bash
# 512x512 vs 1024x1024 对比
PYTHONPATH=/home/<USER>/repos/Mask2Former_v2 \
python tools/debug/features/feature_map_visualizer.py \
    --configs work_dirs/hc_rs10_q3_with_floor_2_0808_512x512/mask2former_config.py \
              work_dirs/hc_rs10_q3_with_floor_2_0808_1024x1024/mask2former_config.py \
    --checkpoints work_dirs/hc_rs10_q3_with_floor_2_0808_512x512/best_coco_segm_mAP_85_epoch_600.pth \
                  work_dirs/hc_rs10_q3_with_floor_2_0808_1024x1024/best_coco_segm_mAP_85_epoch_235.pth \
    --image-name "cuishanlantian_res_ff_RS10_11" \
    --data-root /home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0804_1024x1024 \
    --output-dir output/feature_visualizations
```

## 🎯 预期结果

成功运行后，您应该看到：
1. ✅ 模型加载成功
2. ✅ 找到COCO注释和图像
3. ✅ Stage 0特征提取成功（256×256分辨率）
4. ✅ 生成特征对比可视化
5. ✅ 特征图与原始密度图空间对应

输出文件：
- `cuishanlantian_res_ff_RS10_11_feature_comparison.png`
- `cuishanlantian_res_ff_RS10_11_colorized_features.png`

## 📞 获取帮助

如果问题仍然存在：
1. 检查conda环境是否正确激活
2. 验证所有文件路径是否存在
3. 确认CUDA/GPU设置
4. 尝试使用CPU模式进行测试
5. 检查Python版本兼容性

工具已经过完整测试和修复，应该能够正常工作！
