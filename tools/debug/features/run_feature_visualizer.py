#!/usr/bin/env python3
"""
Wrapper script for feature map visualizer

This script ensures proper environment setup and runs the feature visualizer
from the correct directory with proper PYTHONPATH configuration.
"""

import os
import sys
import subprocess
from pathlib import Path

def setup_environment():
    """Setup the environment for running the feature visualizer"""
    # Get the project root directory
    current_dir = Path(__file__).parent.absolute()
    project_root = current_dir.parent.parent.parent
    
    print(f"🔧 Setting up environment...")
    print(f"📁 Project root: {project_root}")
    print(f"📁 Current directory: {current_dir}")
    
    # Set PYTHONPATH to include project root and tools directory
    pythonpath = [
        str(project_root),
        str(project_root / "tools"),
        str(current_dir)
    ]
    
    # Add existing PYTHONPATH if it exists
    existing_pythonpath = os.environ.get('PYTHONPATH', '')
    if existing_pythonpath:
        pythonpath.append(existing_pythonpath)
    
    env = os.environ.copy()
    env['PYTHONPATH'] = ':'.join(pythonpath)
    
    print(f"🐍 PYTHONPATH: {env['PYTHONPATH']}")
    
    return env, project_root

def install_missing_dependencies():
    """Install missing dependencies if possible"""
    print("🔍 Checking for missing dependencies...")
    
    missing_deps = []
    
    try:
        import colorhash
        print("✅ colorhash available")
    except ImportError:
        missing_deps.append("colorhash")
        print("❌ colorhash missing")
    
    try:
        import plotly
        print("✅ plotly available")
    except ImportError:
        missing_deps.append("plotly")
        print("❌ plotly missing")
    
    if missing_deps:
        print(f"\n⚠️  Missing optional dependencies: {', '.join(missing_deps)}")
        print("Installing with pip...")
        
        for dep in missing_deps:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
                print(f"✅ Installed {dep}")
            except subprocess.CalledProcessError:
                print(f"❌ Failed to install {dep}")
                print(f"   You can install manually: pip install {dep}")

def main():
    """Main wrapper function"""
    print("🚀 Feature Map Visualizer Wrapper")
    print("=" * 50)
    
    # Setup environment
    env, project_root = setup_environment()
    
    # Try to install missing dependencies
    install_missing_dependencies()
    
    # Change to project root directory
    original_cwd = os.getcwd()
    os.chdir(project_root)
    print(f"📂 Changed working directory to: {project_root}")
    
    try:
        # Build command
        script_path = Path(__file__).parent / "feature_map_visualizer.py"
        cmd = [sys.executable, str(script_path)] + sys.argv[1:]
        
        print(f"\n🎯 Running command:")
        print(f"   {' '.join(cmd)}")
        print(f"   Working directory: {os.getcwd()}")
        print(f"   PYTHONPATH: {env.get('PYTHONPATH', 'Not set')}")
        
        # Run the feature visualizer
        result = subprocess.run(cmd, env=env, cwd=project_root)
        return result.returncode
        
    except KeyboardInterrupt:
        print("\n⚠️  Interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Error running feature visualizer: {e}")
        return 1
    finally:
        # Restore original working directory
        os.chdir(original_cwd)

if __name__ == '__main__':
    exit(main())
