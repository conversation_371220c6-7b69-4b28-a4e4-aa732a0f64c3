#!/usr/bin/env python3
"""
Test the reshape fix for SwinTransformer features
"""

import torch
import numpy as np

def test_reshape_logic():
    """Test the reshape logic for SwinTransformer features"""
    
    print("🧪 Testing SwinTransformer Feature Reshape")
    print("=" * 50)
    
    # Simulate SwinTransformer Stage 0 output
    # For 1024x1024 input with stride 4, we get 256x256 patches
    B, N, C = 1, 65536, 192  # From your log: torch.Size([1, 65536, 192])
    
    print(f"📊 Original SwinTransformer output shape: [{B}, {N}, {C}]")
    print(f"  - Batch size: {B}")
    print(f"  - Number of patches: {N}")
    print(f"  - Feature dimension: {C}")
    
    # Create dummy tensor
    stage0_feature = torch.randn(B, N, C)
    
    # Apply reshape logic
    if len(stage0_feature.shape) == 3:
        B, N, C = stage0_feature.shape
        # For 1024x1024 input with stride 4, we expect 256x256 patches
        H = W = int(N ** 0.5)  # sqrt(65536) = 256
        print(f"📐 Calculated spatial dimensions: {H}×{W}")
        print(f"  - H * W = {H * W}, N = {N}")
        
        if H * W == N:
            # Reshape to [B, C, H, W] format
            reshaped_feature = stage0_feature.transpose(1, 2).reshape(B, C, H, W)
            print(f"✅ Successfully reshaped to: {reshaped_feature.shape}")
            print(f"  - Format: [Batch, Channels, Height, Width]")
            print(f"  - Spatial resolution: {H}×{W}")
            print(f"  - Stride from 1024×1024 input: {1024//H}")
            
            # Verify the reshape is correct
            print(f"\n🔍 Verification:")
            print(f"  - Original tensor memory: {stage0_feature.numel()} elements")
            print(f"  - Reshaped tensor memory: {reshaped_feature.numel()} elements")
            print(f"  - Memory preserved: {stage0_feature.numel() == reshaped_feature.numel()}")
            
            # Test a specific element to ensure reshape is correct
            original_element = stage0_feature[0, 0, 0]  # First patch, first feature
            reshaped_element = reshaped_feature[0, 0, 0, 0]  # First channel, top-left pixel
            print(f"  - Element preservation: {torch.allclose(original_element, reshaped_element)}")
            
            return True
        else:
            print(f"❌ Unexpected patch count: {N}, expected square number")
            return False
    else:
        print(f"❌ Unexpected tensor shape: {stage0_feature.shape}")
        return False

def test_visualization_compatibility():
    """Test if reshaped features are compatible with visualization functions"""
    
    print(f"\n🎨 Testing Visualization Compatibility")
    print("=" * 50)
    
    # Create reshaped feature tensor
    B, C, H, W = 1, 192, 256, 256
    features = torch.randn(B, C, H, W)
    
    print(f"📊 Feature tensor shape: {features.shape}")
    
    # Test channel selection logic (simplified)
    def select_diverse_channels(features, n_channels=8):
        """Simplified channel selection"""
        if len(features.shape) != 4:
            print(f"❌ Expected 4D tensor, got {len(features.shape)}D")
            return None
        
        C = features.shape[1]
        if n_channels >= C:
            return list(range(C))
        
        # Simple uniform sampling
        step = C // n_channels
        selected = list(range(0, C, step))[:n_channels]
        return selected
    
    selected_channels = select_diverse_channels(features, 8)
    if selected_channels:
        print(f"✅ Selected {len(selected_channels)} channels: {selected_channels}")
        
        # Test feature extraction
        selected_features = features[0, selected_channels]  # [n_channels, H, W]
        print(f"✅ Extracted features shape: {selected_features.shape}")
        
        # Test normalization
        normalized_features = []
        for i, channel_idx in enumerate(selected_channels):
            feat = features[0, channel_idx]  # [H, W]
            feat_norm = (feat - feat.min()) / (feat.max() - feat.min() + 1e-8)
            normalized_features.append(feat_norm)
        
        print(f"✅ Normalized {len(normalized_features)} feature maps")
        print(f"  - Each feature map shape: {normalized_features[0].shape}")
        
        return True
    else:
        print(f"❌ Channel selection failed")
        return False

def main():
    """Main test function"""
    print("🧪 SwinTransformer Feature Reshape Test")
    print("=" * 60)
    
    test1_passed = test_reshape_logic()
    test2_passed = test_visualization_compatibility()
    
    print(f"\n📊 Test Results:")
    print(f"  - Reshape logic: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"  - Visualization compatibility: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print(f"\n🎉 All tests passed!")
        print(f"💡 The feature visualizer should work correctly now")
        print(f"🔍 Key findings:")
        print(f"  - SwinTransformer outputs [B, N, C] format")
        print(f"  - N=65536 patches for 1024×1024 input (256×256 spatial)")
        print(f"  - Reshape to [B, C, H, W] works correctly")
        print(f"  - Features are compatible with visualization")
    else:
        print(f"\n❌ Some tests failed")
        print(f"🔧 Please check the reshape logic")
    
    return 0 if (test1_passed and test2_passed) else 1

if __name__ == '__main__':
    exit(main())
