#!/usr/bin/env python3
"""
Feature Map Visualizer for Multi-Resolution Comparison

This script visualizes feature maps from networks trained on different input resolutions
to demonstrate that high-resolution inputs enable the network to learn feature maps 
with more geometric details.

Usage:
    python feature_map_visualizer.py \
        --configs config1.py config2.py \
        --checkpoints checkpoint1.pth checkpoint2.pth \
        --image-name "cuishanlantian_res_ff_RS10_11" \
        --output-dir ./feature_visualizations
"""

import os
import sys
import argparse
import numpy as np
import cv2
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
from matplotlib.gridspec import GridSpec

# Import torch at the top level
try:
    import torch
    import torch.nn.functional as F
    import torch.nn as nn
    TORCH_AVAILABLE = True
    print("✅ PyTorch imported successfully")
except ImportError as e:
    print(f"❌ PyTorch import failed: {e}")
    TORCH_AVAILABLE = False

# Add project root to path
current_file = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_file)))
print(f"🔧 Project root: {project_root}")

# Clear any duplicate paths first
paths_to_add = [project_root]
for path in paths_to_add:
    if path in sys.path:
        sys.path.remove(path)
    sys.path.insert(0, path)

print(f"🐍 Updated sys.path (first 5 entries):")
for i, path in enumerate(sys.path[:5]):
    print(f"  {i}: {path}")

# Import MMDetection components
try:
    from mmdet.apis import init_detector
    from mmdet.structures import DetDataSample
    from mmcv.transforms import Compose
    import torch.nn as nn
    MMDET_AVAILABLE = True
    print("✅ MMDetection components loaded successfully")
except ImportError as e:
    print(f"❌ MMDetection import failed: {e}")
    MMDET_AVAILABLE = False

# Import colorization utilities
COLORIZATION_AVAILABLE = False
try:
    # Try importing colorhash first
    from colorhash import ColorHash
    try:
        from plotly.colors import sample_colorscale, get_colorscale
        PLOTLY_AVAILABLE = True
    except ImportError:
        PLOTLY_AVAILABLE = False
        print("⚠️  Plotly not available, using fallback coloring")

    # Try importing local color utilities
    try:
        from .color import feats_to_plotly_rgb, min_max_normalize
        from .features import rgb2hsv, rgb2lab
        COLORIZATION_AVAILABLE = True
        print("✅ Colorization utilities loaded successfully")
    except ImportError:
        try:
            from color import feats_to_plotly_rgb, min_max_normalize
            from features import rgb2hsv, rgb2lab
            COLORIZATION_AVAILABLE = True
            print("✅ Colorization utilities loaded successfully")
        except ImportError:
            # Create fallback functions
            print("⚠️  Color utilities not found, using fallback implementations")
            def feats_to_plotly_rgb(feats, normalize=False, colorscale=None):
                """Fallback colorization using simple normalization"""
                if feats.dim() == 1:
                    feats = feats.unsqueeze(1)

                if feats.shape[1] >= 3:
                    # Use first 3 channels as RGB
                    color = feats[:, :3]
                elif feats.shape[1] == 1:
                    # Grayscale to RGB
                    color = feats.repeat(1, 3)
                else:
                    # Pad to 3 channels
                    color = torch.cat([feats, torch.zeros(feats.shape[0], 3-feats.shape[1])], dim=1)

                if normalize:
                    color = (color - color.min()) / (color.max() - color.min() + 1e-8)

                return (color * 255).clamp(0, 255).numpy().astype(np.uint8)

            def min_max_normalize(x):
                """Fallback normalization"""
                return (x - x.min()) / (x.max() - x.min() + 1e-8)

            COLORIZATION_AVAILABLE = True

except ImportError as e:
    print(f"❌ ColorHash not available: {e}")
    print("⚠️  Advanced coloring features will be limited")
    # Create minimal fallback
    def feats_to_plotly_rgb(feats, normalize=False, colorscale=None):
        """Minimal fallback colorization"""
        if feats.dim() == 1:
            feats = feats.unsqueeze(1)
        # Simple grayscale
        mean_feat = feats.mean(dim=1, keepdim=True)
        if normalize:
            mean_feat = (mean_feat - mean_feat.min()) / (mean_feat.max() - mean_feat.min() + 1e-8)
        color = mean_feat.repeat(1, 3)
        return (color * 255).clamp(0, 255).numpy().astype(np.uint8)

    def min_max_normalize(x):
        return (x - x.min()) / (x.max() - x.min() + 1e-8)

    COLORIZATION_AVAILABLE = True

# Import config utilities
try:
    from configs.mask2former_config import set_img_size
    CONFIG_AVAILABLE = True
    print(f"✅ Config utilities loaded, image size: {set_img_size}")
except ImportError:
    try:
        # Try importing from project root
        sys.path.insert(0, project_root)
        from configs.mask2former_config import set_img_size
        CONFIG_AVAILABLE = True
        print(f"✅ Config utilities loaded, image size: {set_img_size}")
    except ImportError as e:
        print(f"⚠️  Config utilities import failed: {e}")
        print("Using default image size: 512")
        set_img_size = 512
        CONFIG_AVAILABLE = False


class FeatureExtractor:
    """Feature extraction hook for capturing intermediate feature maps"""
    
    def __init__(self):
        self.features = {}
        self.hooks = []
    
    def hook_fn(self, name):
        """Create a hook function for a specific layer"""
        def hook(module, input, output):
            if isinstance(output, (list, tuple)):
                # Handle multi-level outputs (e.g., from backbone)
                # Some outputs might be tuples with non-tensor elements
                processed_output = []
                for i, feat in enumerate(output):
                    if hasattr(feat, 'detach'):  # Check if it's a tensor
                        processed_output.append(feat.detach().cpu())
                    else:
                        # Non-tensor output (e.g., shape info), store as-is
                        processed_output.append(feat)
                self.features[name] = processed_output
            else:
                # Handle single tensor output
                if hasattr(output, 'detach'):  # Check if it's a tensor
                    self.features[name] = output.detach().cpu()
                else:
                    self.features[name] = output
        return hook
    
    def register_hooks(self, model, target_layers: List[str] = None):
        """Register hooks for target layers. If None, register for backbone stages."""
        if target_layers is None:
            # Default: register for all backbone stages, focusing on stage 0 (highest resolution)
            target_layers = ['backbone.stages.0', 'backbone.stages.1', 'backbone.stages.2', 'backbone.stages.3']

        registered_count = 0
        for name, module in model.named_modules():
            if any(target in name for target in target_layers):
                # Only register for the main stage modules, not sub-blocks
                if name.count('.') == 2 and name.endswith(('stages.0', 'stages.1', 'stages.2', 'stages.3')):
                    hook = module.register_forward_hook(self.hook_fn(name))
                    self.hooks.append(hook)
                    print(f"📌 Registered hook: {name}")
                    registered_count += 1

        if registered_count == 0:
            print(f"⚠️  No hooks registered for targets: {target_layers}")
            print("Available backbone layers:")
            for name, _ in model.named_modules():
                if 'backbone' in name and 'stages' in name:
                    print(f"  - {name}")
        else:
            print(f"✅ Registered {registered_count} hooks")
    
    def remove_hooks(self):
        """Remove all registered hooks"""
        for hook in self.hooks:
            hook.remove()
        self.hooks.clear()
    
    def get_features(self) -> Dict:
        """Get extracted features, filtering out non-tensor elements"""
        filtered_features = {}
        for name, feature in self.features.items():
            if isinstance(feature, (list, tuple)):
                # Filter to only tensor elements
                tensor_features = [f for f in feature if hasattr(f, 'detach')]
                if tensor_features:
                    filtered_features[name] = tensor_features
            elif hasattr(feature, 'detach'):
                filtered_features[name] = feature
        return filtered_features
    
    def clear_features(self):
        """Clear stored features"""
        self.features.clear()


class MultiResolutionFeatureVisualizer:
    """
    Visualizer for comparing feature maps from networks trained on different resolutions
    """
    
    def __init__(self, output_dir: str = "./feature_visualizations"):
        """
        Initialize the visualizer
        
        Args:
            output_dir: Directory to save visualization results
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.models = {}
        self.configs = {}
        self.extractors = {}
        
        print(f"🎨 Feature visualizer initialized")
        print(f"📁 Output directory: {self.output_dir}")
    
    def load_models(self, config_paths: List[str], checkpoint_paths: List[str], 
                   device: str = "cuda:0") -> bool:
        """
        Load multiple models with different configurations
        
        Args:
            config_paths: List of config file paths
            checkpoint_paths: List of checkpoint file paths
            device: Device for model inference
            
        Returns:
            True if all models loaded successfully
        """
        if not MMDET_AVAILABLE:
            print("❌ MMDetection not available, cannot load models")
            return False
        
        if len(config_paths) != len(checkpoint_paths):
            print("❌ Number of configs and checkpoints must match")
            return False
        
        success_count = 0
        
        for i, (config_path, checkpoint_path) in enumerate(zip(config_paths, checkpoint_paths)):
            try:
                print(f"\n🔧 Loading model {i+1}/{len(config_paths)}")
                print(f"  Config: {config_path}")
                print(f"  Checkpoint: {checkpoint_path}")
                
                # Verify files exist
                if not os.path.exists(config_path):
                    print(f"❌ Config file not found: {config_path}")
                    continue
                    
                if not os.path.exists(checkpoint_path):
                    print(f"❌ Checkpoint file not found: {checkpoint_path}")
                    continue
                
                # Load model with better error handling
                try:
                    model = init_detector(config_path, checkpoint_path, device=device)
                except ImportError as import_err:
                    if "custom_imports" in str(import_err) or "custom_metrics" in str(import_err):
                        print(f"⚠️  Custom imports issue, trying to modify config temporarily...")
                        try:
                            # Try to load config and modify custom_imports
                            from mmengine.config import Config
                            cfg = Config.fromfile(config_path)

                            # Temporarily disable custom imports
                            if hasattr(cfg, 'custom_imports'):
                                print(f"  📝 Disabling custom_imports temporarily...")
                                cfg.custom_imports = dict(imports=[], allow_failed_imports=True)

                            # Save temporary config
                            import tempfile
                            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                                cfg.dump(f.name)
                                temp_config = f.name

                            try:
                                print(f"  🔄 Retrying with modified config...")
                                model = init_detector(temp_config, checkpoint_path, device=device)
                                print(f"✅ Model loaded with modified config")
                            finally:
                                # Clean up temp file
                                if os.path.exists(temp_config):
                                    os.unlink(temp_config)
                        except Exception as e:
                            print(f"❌ Failed to modify config: {e}")
                            raise import_err
                    else:
                        raise import_err
                
                # Extract resolution info from config path for naming
                config_name = Path(config_path).parent.name
                resolution_info = self._extract_resolution_info(config_name)
                
                model_key = f"model_{resolution_info}_{i}"
                self.models[model_key] = model
                self.configs[model_key] = config_path
                
                # Initialize feature extractor
                extractor = FeatureExtractor()
                # Register hooks for backbone (highest resolution features)
                extractor.register_hooks(model, ['backbone'])
                self.extractors[model_key] = extractor
                
                print(f"✅ Model {model_key} loaded successfully")
                success_count += 1
                
            except Exception as e:
                print(f"❌ Failed to load model {i+1}: {e}")
                import traceback
                traceback.print_exc()
        
        print(f"\n📊 Successfully loaded {success_count}/{len(config_paths)} models")
        return success_count > 0
    
    def _extract_resolution_info(self, config_name: str) -> str:
        """Extract resolution information from config directory name"""
        # Look for resolution patterns like 1024x1024, 512x512, etc.
        import re
        resolution_match = re.search(r'(\d+)x(\d+)', config_name)
        if resolution_match:
            return f"{resolution_match.group(1)}x{resolution_match.group(2)}"
        else:
            # Fallback to using part of the config name
            return config_name.split('_')[-1] if '_' in config_name else config_name

    def find_projection_image_coco(self, image_name: str, data_root: str,
                                 ann_file: str = None) -> Optional[str]:
        """
        Find projection density image using COCO format data structure

        Args:
            image_name: Base name of the image (e.g., "cuishanlantian_res_ff_RS10_11")
            data_root: Root directory containing COCO format density maps
            ann_file: COCO annotation file path (optional)

        Returns:
            Path to the found image or None
        """
        print(f"🔍 Searching for '{image_name}' in COCO format data")
        print(f"📁 Data root: {data_root}")

        if not os.path.exists(data_root):
            print(f"❌ Data root directory not found: {data_root}")
            return None

        # If annotation file not specified, look for it in data_root/annotations/
        if ann_file is None:
            # Try common annotation file names
            ann_dir = os.path.join(data_root, 'annotations')
            possible_ann_files = [
                'train.json',
                'val.json',
                'test.json',
                'instances_train.json',
                'instances_val.json',
                'instances_test.json',
                'annotations.json'
            ]

            print(f"🔍 Looking for annotation files in: {ann_dir}")

            if os.path.exists(ann_dir):
                for ann_name in possible_ann_files:
                    candidate_path = os.path.join(ann_dir, ann_name)
                    if os.path.exists(candidate_path):
                        ann_file = candidate_path
                        print(f"✅ Found annotation file: {ann_file}")
                        break

            if ann_file is None:
                print(f"❌ No annotation file found in {ann_dir}")
                print(f"💡 Tried: {possible_ann_files}")
                return None

        if not os.path.exists(ann_file):
            print(f"❌ Annotation file not found: {ann_file}")
            return None

        try:
            # Load COCO annotations to get image information
            from pycocotools.coco import COCO
            coco = COCO(ann_file)
            print(f"✅ Loaded COCO annotations: {len(coco.getImgIds())} images")

            # Search for the image by filename
            target_filename = f"{image_name}.png"  # Assume PNG format

            for img_id in coco.getImgIds():
                img_info = coco.loadImgs(img_id)[0]
                img_filename = img_info['file_name']

                # Check if this is the target image
                if os.path.splitext(img_filename)[0] == image_name:
                    # Found the image, construct full path
                    image_path = os.path.join(data_root, img_filename)

                    if os.path.exists(image_path):
                        print(f"✅ Found image: {image_path}")
                        print(f"📊 Image info: {img_info['width']}x{img_info['height']}")
                        return image_path
                    else:
                        print(f"⚠️  Image file not found at expected path: {image_path}")

                        # Try searching in subdirectories
                        for subdir in ['train', 'val', 'test', 'images']:
                            alt_path = os.path.join(data_root, subdir, img_filename)
                            if os.path.exists(alt_path):
                                print(f"✅ Found image in {subdir}: {alt_path}")
                                return alt_path

            print(f"❌ Image '{image_name}' not found in COCO annotations")

            # Fallback: direct file search
            print(f"🔍 Fallback: searching directly in directory...")
            extensions = ['.png', '.jpg', '.jpeg']

            for root, dirs, files in os.walk(data_root):
                for file in files:
                    name_without_ext = os.path.splitext(file)[0]
                    if name_without_ext == image_name and any(file.lower().endswith(ext) for ext in extensions):
                        full_path = os.path.join(root, file)
                        print(f"✅ Found image (fallback): {full_path}")
                        return full_path

            return None

        except Exception as e:
            print(f"❌ Error loading COCO annotations: {e}")
            return None

    def list_available_images(self, projection_data_dirs: List[str] = None) -> Dict[str, List[str]]:
        """
        List all available images in projection directories

        Args:
            projection_data_dirs: List of projection data directories to search in

        Returns:
            Dictionary mapping directory paths to lists of available images
        """
        if projection_data_dirs is None:
            projection_data_dirs = [
                "/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0722_256x256/",
                "/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0731_512x512/",
                "/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0804_1024x1024/",
                # Fallback to output directories if the above paths don't exist
                "output/hc_rs10_q2_wo_floor_2_0722_256x256_test/",
                "output/hc_rs10_q2_wo_floor_2_0731_512x512_test/",
                "output/hc_rs10_q2_wo_floor_2_0804_1024x1024_epoch_1850_test/",
                "output/hc_rs10_q2_wo_floor_2_0804_1024x1024_epoch_1850_train/",
                "output/hc_rs10_q2_wo_floor_2_0804_1024x1024_epoch_1850_val/",
            ]

        all_images = {}
        extensions = ['.png', '.jpg', '.jpeg']

        print("📋 Available images in projection directories:")
        print("=" * 60)

        for proj_dir in projection_data_dirs:
            if not os.path.exists(proj_dir):
                print(f"⚠️  Directory not found: {proj_dir}")
                continue

            dir_images = {}
            print(f"\n📁 {proj_dir}")

            # First check the main directory
            try:
                files = os.listdir(proj_dir)
                image_files = []

                for file in files:
                    if any(file.lower().endswith(ext) for ext in extensions):
                        name_without_ext = os.path.splitext(file)[0]
                        # Remove _result suffix if present
                        if name_without_ext.endswith('_result'):
                            name_without_ext = name_without_ext[:-7]
                        image_files.append(name_without_ext)

                if image_files:
                    # Determine subset from directory name
                    subset_name = "main"
                    if "train" in proj_dir:
                        subset_name = "train"
                    elif "val" in proj_dir:
                        subset_name = "val"
                    elif "test" in proj_dir:
                        subset_name = "test"

                    dir_images[subset_name] = sorted(list(set(image_files)))  # Remove duplicates
                    print(f"  📂 {subset_name}: {len(dir_images[subset_name])} images")
                    # Show first few examples
                    for i, img in enumerate(dir_images[subset_name][:5]):
                        print(f"    • {img}")
                    if len(dir_images[subset_name]) > 5:
                        print(f"    ... and {len(dir_images[subset_name]) - 5} more")
                else:
                    print(f"  📂 main: No images found")

            except OSError as e:
                print(f"  ❌ Error reading {proj_dir}: {e}")

            # Also check subdirectories
            for subset in ['train', 'val', 'test']:
                subset_dir = os.path.join(proj_dir, subset)
                if not os.path.exists(subset_dir):
                    continue

                try:
                    files = os.listdir(subset_dir)
                    image_files = []

                    for file in files:
                        if any(file.lower().endswith(ext) for ext in extensions):
                            name_without_ext = os.path.splitext(file)[0]
                            # Remove _result suffix if present
                            if name_without_ext.endswith('_result'):
                                name_without_ext = name_without_ext[:-7]
                            image_files.append(name_without_ext)

                    if image_files:
                        dir_images[subset] = sorted(list(set(image_files)))  # Remove duplicates
                        print(f"  📂 {subset}: {len(dir_images[subset])} images")
                        # Show first few examples
                        for i, img in enumerate(dir_images[subset][:3]):
                            print(f"    • {img}")
                        if len(dir_images[subset]) > 3:
                            print(f"    ... and {len(dir_images[subset]) - 3} more")
                    else:
                        print(f"  📂 {subset}: No images found")

                except OSError as e:
                    print(f"  ❌ Error reading {subset_dir}: {e}")

            if dir_images:
                all_images[proj_dir] = dir_images

        return all_images

    def extract_features(self, image_path: str) -> Dict:
        """
        Extract features from all loaded models for a given image

        Args:
            image_path: Path to the input image

        Returns:
            Dictionary mapping model keys to extracted features
        """
        if not os.path.exists(image_path):
            print(f"❌ Image not found: {image_path}")
            return {}

        print(f"\n🔍 Extracting features from: {os.path.basename(image_path)}")

        # Load image
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ Failed to load image: {image_path}")
            return {}

        # Convert BGR to RGB
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        print(f"📐 Image shape: {image_rgb.shape}")

        all_features = {}

        for model_key, model in self.models.items():
            try:
                print(f"\n🤖 Processing with {model_key}")

                # Clear previous features
                self.extractors[model_key].clear_features()

                # Perform inference with proper preprocessing (this will trigger the hooks)
                with torch.no_grad():
                    # Use the standard MMDetection inference API with proper preprocessing
                    from mmdet.apis import inference_detector

                    print(f"🔍 Running inference with proper preprocessing...")
                    print(f"  📐 Original image shape: {image_rgb.shape}")

                    # The inference_detector will handle all preprocessing according to the model config:
                    # 1. BGR conversion (bgr_to_rgb=True in config)
                    # 2. Resize with keep_ratio=True to (1024, 1024)
                    # 3. Normalization with mean=[19.24, 19.24, 19.24], std=[39.99, 39.99, 39.99]
                    # 4. Padding to (1024, 1024) with BatchFixedSizePad

                    # Run inference - this will trigger the hooks and apply correct preprocessing
                    result = inference_detector(model, image_path)
                    print(f"✅ Inference completed successfully")

                    # Get the actual input shape after preprocessing
                    if hasattr(result, 'metainfo') and 'img_shape' in result.metainfo:
                        processed_shape = result.metainfo['img_shape']
                        print(f"  📐 Processed image shape: {processed_shape}")
                    else:
                        print(f"  📐 Processed image shape: (1024, 1024) [from config]")

                # Get extracted features
                features = self.extractors[model_key].get_features()
                all_features[model_key] = features

                print(f"✅ Extracted features from {len(features)} layers")
                for layer_name, feature in features.items():
                    if isinstance(feature, list):
                        print(f"  {layer_name}: {len(feature)} levels")
                        for i, feat in enumerate(feature):
                            print(f"    Level {i}: {feat.shape}")
                    else:
                        print(f"  {layer_name}: {feature.shape}")

            except Exception as e:
                print(f"❌ Feature extraction failed for {model_key}: {e}")
                import traceback
                traceback.print_exc()

        return all_features

    def _select_stage0_features(self, all_features: Dict) -> Dict:
        """
        Select Stage 0 (highest resolution, stride 4) features from all extracted features

        Args:
            all_features: Dictionary of all extracted features from different models

        Returns:
            Dictionary with Stage 0 features for each model, maintaining the expected structure
        """
        stage0_features = {}

        for model_key, features in all_features.items():
            print(f"🔍 Selecting Stage 0 features for {model_key}...")

            # Look for backbone.stages.0 features (highest resolution)
            stage0_feature = None
            stage0_layer_name = None

            for layer_name, feature in features.items():
                if 'backbone.stages.0' in layer_name:
                    if isinstance(feature, list):
                        # Take the last output from stage 0 (after all blocks)
                        stage0_feature = feature[-1] if feature else None
                    else:
                        stage0_feature = feature
                    stage0_layer_name = layer_name
                    break

            if stage0_feature is not None and hasattr(stage0_feature, 'shape'):
                # Convert SwinTransformer 3D output [B, N, C] to 4D [B, C, H, W]
                if len(stage0_feature.shape) == 3:
                    B, N, C = stage0_feature.shape
                    # For 1024x1024 input with stride 4, we expect 256x256 patches
                    H = W = int(N ** 0.5)  # sqrt(65536) = 256
                    if H * W == N:
                        # Reshape to [B, C, H, W] format
                        stage0_feature = stage0_feature.transpose(1, 2).reshape(B, C, H, W)
                        print(f"✅ Reshaped SwinTransformer output: {stage0_feature.shape}")
                    else:
                        print(f"⚠️  Unexpected patch count: {N}, expected square number")

                # Create a dictionary structure that matches what visualize_highest_resolution_features expects
                stage0_features[model_key] = {stage0_layer_name: stage0_feature}
                print(f"✅ Selected Stage 0 features: {stage0_feature.shape}")
                if len(stage0_feature.shape) == 4:
                    print(f"  📐 Resolution: {stage0_feature.shape[-2]}×{stage0_feature.shape[-1]} (stride 4)")
                else:
                    print(f"  📐 Shape: {stage0_feature.shape}")
            else:
                print(f"❌ No Stage 0 features found for {model_key}")
                # Fallback: try to find any backbone feature
                for layer_name, feature in features.items():
                    if 'backbone' in layer_name and hasattr(feature, 'shape'):
                        fallback_feature = None
                        if isinstance(feature, list) and feature:
                            fallback_feature = feature[0]  # Take first level
                        elif not isinstance(feature, list):
                            fallback_feature = feature

                        if fallback_feature is not None:
                            # Convert SwinTransformer 3D output to 4D if needed
                            if len(fallback_feature.shape) == 3:
                                B, N, C = fallback_feature.shape
                                H = W = int(N ** 0.5)
                                if H * W == N:
                                    fallback_feature = fallback_feature.transpose(1, 2).reshape(B, C, H, W)
                                    print(f"⚠️  Reshaped fallback feature: {fallback_feature.shape}")

                            stage0_features[model_key] = {layer_name: fallback_feature}
                            print(f"⚠️  Using fallback feature: {layer_name}, shape: {fallback_feature.shape}")
                            break

        return stage0_features

    def visualize_highest_resolution_features(self, all_features: Dict,
                                            image_name: str,
                                            max_channels: int = 16) -> str:
        """
        Visualize the highest resolution feature maps from all models

        Args:
            all_features: Dictionary of extracted features from all models
            image_name: Name of the input image for saving
            max_channels: Maximum number of channels to visualize per model

        Returns:
            Path to the saved visualization
        """
        if not all_features:
            print("❌ No features to visualize")
            return ""

        print(f"\n🎨 Creating feature map visualization")

        # Find highest resolution features for each model
        model_features = {}

        for model_key, features in all_features.items():
            highest_res_features = None
            min_stride = float('inf')

            for layer_name, feature in features.items():
                if 'backbone' in layer_name:
                    if isinstance(feature, list):
                        # Backbone outputs multi-level features
                        # Level 0 has the highest resolution (smallest stride)
                        if len(feature) > 0:
                            highest_res_features = feature[0]  # First level (stride 4)
                            min_stride = 4
                            break
                    elif hasattr(feature, 'shape') and len(feature.shape) == 4:
                        # Single tensor feature (already processed)
                        highest_res_features = feature
                        # Infer stride from spatial resolution
                        H = feature.shape[2]
                        if H == 256:  # 1024/4 = 256
                            min_stride = 4
                        elif H == 128:  # 1024/8 = 128
                            min_stride = 8
                        else:
                            min_stride = 1024 // H
                        break

            if highest_res_features is not None:
                model_features[model_key] = highest_res_features
                print(f"📊 {model_key}: {highest_res_features.shape} (stride {min_stride})")

        if not model_features:
            print("❌ No suitable features found for visualization")
            return ""

        # Create visualization
        n_models = len(model_features)
        n_channels = min(max_channels, min(feat.shape[1] for feat in model_features.values()))

        # Create figure with subplots - clean layout without spacing
        fig = plt.figure(figsize=(2 * n_channels, 2 * n_models))
        gs = GridSpec(n_models, n_channels, figure=fig, hspace=0, wspace=0)

        # No title for clean visualization

        for model_idx, (model_key, features) in enumerate(model_features.items()):
            # Extract batch (assume batch size = 1)
            if features.dim() == 4:
                features = features[0]  # Remove batch dimension

            print(f"🎨 Visualizing {model_key}: {features.shape}")

            # Select channels to visualize
            channel_indices = self._select_diverse_channels(features, n_channels)

            for ch_idx, channel_idx in enumerate(channel_indices):
                ax = fig.add_subplot(gs[model_idx, ch_idx])

                # Get feature map for this channel
                feature_map = features[channel_idx].numpy()

                # Enhanced normalization for better contrast
                # Use percentile-based normalization to enhance contrast
                p2, p98 = np.percentile(feature_map, [2, 98])
                feature_map = np.clip(feature_map, p2, p98)
                feature_map = (feature_map - p2) / (p98 - p2 + 1e-8)

                # Display feature map with high contrast colormap
                im = ax.imshow(feature_map, cmap='hot', aspect='equal', interpolation='nearest')

                # Remove all decorations for clean visualization
                ax.set_xticks([])
                ax.set_yticks([])
                ax.spines['top'].set_visible(False)
                ax.spines['right'].set_visible(False)
                ax.spines['bottom'].set_visible(False)
                ax.spines['left'].set_visible(False)

        # Get resolution info for filename
        sample_features = next(iter(model_features.values()))
        if sample_features.dim() == 4:
            resolution = f"{sample_features.shape[2]}x{sample_features.shape[3]}"
        else:
            resolution = f"{sample_features.shape[1]}x{sample_features.shape[2]}"

        # Save visualization with clean filename
        output_path = self.output_dir / f"{image_name}_{resolution}_features.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight', pad_inches=0)
        plt.close()

        print(f"💾 Feature visualization saved: {output_path}")
        return str(output_path)

    def _select_diverse_channels(self, features: torch.Tensor, n_channels: int) -> List[int]:
        """
        Select diverse channels for visualization based on activation patterns

        Args:
            features: Feature tensor of shape (C, H, W)
            n_channels: Number of channels to select

        Returns:
            List of selected channel indices
        """
        C, H, W = features.shape

        if n_channels >= C:
            return list(range(C))

        # Calculate activation statistics for each channel
        channel_stats = []
        for c in range(C):
            channel_map = features[c]
            stats = {
                'mean': channel_map.mean().item(),
                'std': channel_map.std().item(),
                'max': channel_map.max().item(),
                'min': channel_map.min().item(),
                'sparsity': (channel_map == 0).float().mean().item()
            }
            channel_stats.append((c, stats))

        # Sort by activation strength (combination of mean and std)
        channel_stats.sort(key=lambda x: x[1]['mean'] + x[1]['std'], reverse=True)

        # Select diverse channels (not just the strongest ones)
        selected = []
        step = max(1, len(channel_stats) // n_channels)

        for i in range(0, min(len(channel_stats), n_channels * step), step):
            selected.append(channel_stats[i][0])

        # Fill remaining slots if needed
        while len(selected) < n_channels and len(selected) < C:
            for c in range(C):
                if c not in selected:
                    selected.append(c)
                    break

        return selected[:n_channels]

    def create_colorized_feature_maps(self, all_features: Dict, image_name: str) -> str:
        """
        Create colorized feature map visualizations using PCA-based coloring

        Args:
            all_features: Dictionary of extracted features from all models
            image_name: Name of the input image for saving

        Returns:
            Path to the saved colorized visualization
        """
        if not COLORIZATION_AVAILABLE:
            print("❌ Colorization utilities not available")
            return ""

        print(f"\n🌈 Creating colorized feature map visualization")

        # Find highest resolution features for each model
        model_features = {}

        for model_key, features in all_features.items():
            for layer_name, feature in features.items():
                if 'backbone' in layer_name:
                    if isinstance(feature, list):
                        if len(feature) > 0:
                            model_features[model_key] = feature[0]  # Highest resolution
                            break
                    elif hasattr(feature, 'shape') and len(feature.shape) == 4:
                        # Single tensor feature (already processed)
                        model_features[model_key] = feature
                        break

        if not model_features:
            print("❌ No suitable features found for colorization")
            return ""

        # Create colorized visualization - clean layout
        n_models = len(model_features)
        fig, axes = plt.subplots(1, n_models, figsize=(4 * n_models, 4))
        if n_models == 1:
            axes = [axes]

        # Remove spacing for clean visualization
        fig.subplots_adjust(left=0, right=1, top=1, bottom=0, wspace=0)

        for idx, (model_key, features) in enumerate(model_features.items()):
            # Extract batch (assume batch size = 1)
            if features.dim() == 4:
                features = features[0]  # Remove batch dimension

            C, H, W = features.shape
            print(f"🎨 Colorizing {model_key}: {features.shape}")

            # Reshape features for PCA colorization
            features_flat = features.view(C, -1).T  # (H*W, C)

            # Try multiple colorization methods for best results
            colorization_success = False

            # Method 1: Multi-channel RGB fusion (Grad-CAM inspired)
            try:
                print(f"🎨 Trying multi-channel RGB fusion for {model_key}...")

                # Select top channels by activation strength
                channel_activations = features.mean(dim=(1, 2))  # Average activation per channel
                top_channels = torch.topk(channel_activations, min(3, C)).indices

                # Create RGB image from top 3 channels
                if len(top_channels) >= 3:
                    r_channel = features[top_channels[0]].numpy()
                    g_channel = features[top_channels[1]].numpy()
                    b_channel = features[top_channels[2]].numpy()
                else:
                    # Use different channels if available, otherwise repeat
                    channels_to_use = list(range(min(3, C)))
                    r_channel = features[channels_to_use[0]].numpy()
                    g_channel = features[channels_to_use[1] if len(channels_to_use) > 1 else 0].numpy()
                    b_channel = features[channels_to_use[2] if len(channels_to_use) > 2 else 0].numpy()

                # Enhanced normalization for better contrast
                def normalize_channel_enhanced(channel):
                    # Use percentile-based normalization for better contrast
                    p1, p99 = np.percentile(channel, [1, 99])
                    channel = np.clip(channel, p1, p99)
                    normalized = (channel - p1) / (p99 - p1 + 1e-8)
                    # Apply gamma correction for better visual perception
                    return np.power(normalized, 0.8)

                r_channel = normalize_channel_enhanced(r_channel)
                g_channel = normalize_channel_enhanced(g_channel)
                b_channel = normalize_channel_enhanced(b_channel)

                # Stack into RGB image
                colored_image = np.stack([r_channel, g_channel, b_channel], axis=2)

                # Display multi-channel heatmap
                axes[idx].imshow(colored_image, aspect='equal')
                print(f"✅ Multi-channel RGB fusion successful for {model_key}")
                colorization_success = True

            except Exception as rgb_error:
                print(f"⚠️  Multi-channel RGB fusion failed: {rgb_error}")

            # Method 2: PCA-based colorization (fallback)
            if not colorization_success:
                try:
                    print(f"🎨 Trying PCA colorization for {model_key}...")
                    colored_features = feats_to_plotly_rgb(features_flat, normalize=True)

                    if isinstance(colored_features, np.ndarray):
                        colored_image = colored_features.reshape(H, W, 3)
                    else:
                        colored_image = colored_features.view(H, W, 3).numpy()

                    # Ensure values are in [0, 1] range
                    colored_image = np.clip(colored_image / 255.0, 0, 1)

                    axes[idx].imshow(colored_image, aspect='equal')
                    print(f"✅ PCA colorization successful for {model_key}")
                    colorization_success = True

                except Exception as pca_error:
                    print(f"⚠️  PCA colorization failed: {pca_error}")

            # Method 3: Enhanced single-channel heatmap (final fallback)
            if not colorization_success:
                print(f"🎨 Using enhanced heatmap for {model_key}...")
                # Use the channel with highest activation variance (most informative)
                channel_variances = features.var(dim=(1, 2))
                best_channel_idx = torch.argmax(channel_variances)
                best_channel = features[best_channel_idx].numpy()

                # Enhanced contrast normalization
                p1, p99 = np.percentile(best_channel, [1, 99])
                best_channel = np.clip(best_channel, p1, p99)
                best_channel = (best_channel - p1) / (p99 - p1 + 1e-8)

                # Use 'turbo' colormap if available, otherwise 'plasma'
                try:
                    axes[idx].imshow(best_channel, cmap='turbo', aspect='equal')
                    print(f"✅ Turbo colormap heatmap for {model_key}")
                except:
                    axes[idx].imshow(best_channel, cmap='plasma', aspect='equal')
                    print(f"✅ Plasma colormap heatmap for {model_key}")

            # Remove all decorations for clean visualization
            axes[idx].set_xticks([])
            axes[idx].set_yticks([])
            axes[idx].spines['top'].set_visible(False)
            axes[idx].spines['right'].set_visible(False)
            axes[idx].spines['bottom'].set_visible(False)
            axes[idx].spines['left'].set_visible(False)

        # Get resolution info for filename
        sample_features = next(iter(model_features.values()))
        if sample_features.dim() == 4:
            resolution = f"{sample_features.shape[2]}x{sample_features.shape[3]}"
        else:
            resolution = f"{sample_features.shape[1]}x{sample_features.shape[2]}"

        # Save colorized visualization with clean filename
        output_path = self.output_dir / f"{image_name}_{resolution}_colorized.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight', pad_inches=0)
        plt.close()

        print(f"💾 Colorized visualization saved: {output_path}")
        return str(output_path)

    def cleanup(self):
        """Clean up resources"""
        for extractor in self.extractors.values():
            extractor.remove_hooks()
        print("🧹 Cleanup completed")


def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Visualize feature maps from networks trained on different input resolutions",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    # Compare two models with different resolutions using COCO format data
    python feature_map_visualizer.py \\
        --configs work_dirs/model_512x512/mask2former_config.py work_dirs/model_1024x1024/mask2former_config.py \\
        --checkpoints work_dirs/model_512x512/best_model.pth work_dirs/model_1024x1024/best_model.pth \\
        --image-name "cuishanlantian_res_ff_RS10_11" \\
        --data-root /home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0804_1024x1024/ \\
        --output-dir ./feature_visualizations

    # Single model with custom annotation file
    python feature_map_visualizer.py \\
        --configs work_dirs/model_1024x1024/mask2former_config.py \\
        --checkpoints work_dirs/model_1024x1024/best_model.pth \\
        --image-name "cuishanlantian_res_ff_RS10_11" \\
        --data-root /home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0804_1024x1024/ \\
        --ann-file /path/to/custom/annotations.json \\
        --output-dir ./visualizations
        """
    )

    parser.add_argument(
        '--configs',
        nargs='+',
        required=True,
        help='List of model configuration files'
    )

    parser.add_argument(
        '--checkpoints',
        nargs='+',
        required=True,
        help='List of model checkpoint files (must match configs order)'
    )

    parser.add_argument(
        '--image-name',
        type=str,
        help='Name of the projection density image to visualize (e.g., "cuishanlantian_res_ff_RS10_11")'
    )

    parser.add_argument(
        '--data-root',
        type=str,
        required=True,
        help='Root directory containing COCO format density maps (e.g., /home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0804_1024x1024/)'
    )

    parser.add_argument(
        '--ann-file',
        type=str,
        help='COCO annotation file path (if not specified, will look for annotations.json in data-root)'
    )



    parser.add_argument(
        '--output-dir',
        type=str,
        default='./feature_visualizations',
        help='Directory to save visualization results (default: ./feature_visualizations)'
    )

    parser.add_argument(
        '--device',
        type=str,
        default='cuda:0',
        help='Device for model inference (default: cuda:0)'
    )

    parser.add_argument(
        '--max-channels',
        type=int,
        default=16,
        help='Maximum number of channels to visualize per model (default: 16)'
    )

    parser.add_argument(
        '--projection-dirs',
        nargs='+',
        help='Projection data directories to search for images (e.g., /home/<USER>/data/RS10_data/hc_rs10_q3_with_floor_2_0808_1024x1024/)'
    )

    parser.add_argument(
        '--list-images',
        action='store_true',
        help='List all available images in projection directories and exit'
    )

    return parser.parse_args()


def main():
    """Main function"""
    args = parse_args()

    print("🚀 Multi-Resolution Feature Map Visualizer")
    print("=" * 60)

    # Validate arguments
    if len(args.configs) != len(args.checkpoints):
        print("❌ Error: Number of configs and checkpoints must match")
        return 1

    if not args.image_name:
        print("❌ Error: --image-name must be provided")
        return 1

    if not args.data_root:
        print("❌ Error: --data-root must be provided")
        return 1

    # Check dependencies
    if not MMDET_AVAILABLE:
        print("❌ Error: MMDetection not available. Please install mmdet.")
        return 1

    # Initialize visualizer
    visualizer = MultiResolutionFeatureVisualizer(args.output_dir)

    # Handle list images option
    if args.list_images:
        projection_dirs = args.projection_dirs if args.projection_dirs else None
        visualizer.list_available_images(projection_dirs)
        return 0

    try:
        # Load models
        print(f"\n📚 Loading {len(args.configs)} models...")
        success = visualizer.load_models(args.configs, args.checkpoints, args.device)
        if not success:
            print("❌ Failed to load models")
            return 1

        # Find image using COCO format data structure
        image_path = visualizer.find_projection_image_coco(
            args.image_name,
            args.data_root,
            args.ann_file
        )
        image_name = args.image_name

        if not image_path:
            print("❌ Image not found")
            print("💡 Use --list-images to see all available images")
            print("💡 Use --projection-dirs to specify custom projection directories")
            return 1

        # Extract features
        print(f"\n🔍 Extracting features...")
        all_features = visualizer.extract_features(image_path)

        if not all_features:
            print("❌ No features extracted")
            return 1

        # Select Stage 0 (highest resolution, stride 4) features
        print(f"\n🔍 Selecting Stage 0 (highest resolution) features...")
        stage0_features = visualizer._select_stage0_features(all_features)

        if not stage0_features:
            print("❌ No Stage 0 features found")
            return 1

        # Create visualizations using Stage 0 features
        print(f"\n🎨 Creating visualizations...")

        # 1. Highest resolution feature comparison
        vis_path1 = visualizer.visualize_highest_resolution_features(
            stage0_features, image_name, args.max_channels
        )

        # 2. Colorized feature maps (if colorization available)
        vis_path2 = ""
        if COLORIZATION_AVAILABLE:
            vis_path2 = visualizer.create_colorized_feature_maps(stage0_features, image_name)
        else:
            print("⚠️  Colorization utilities not available, skipping colorized visualization")

        # Summary
        print(f"\n🎉 Feature visualization completed!")
        print(f"📁 Output directory: {args.output_dir}")
        if vis_path1:
            print(f"📊 Feature comparison: {Path(vis_path1).name}")
        if vis_path2:
            print(f"🌈 Colorized features: {Path(vis_path2).name}")

        return 0

    except Exception as e:
        print(f"❌ Error during visualization: {e}")
        import traceback
        traceback.print_exc()
        return 1

    finally:
        # Cleanup
        visualizer.cleanup()


if __name__ == '__main__':
    exit(main())
