# Feature Map Visualizer

This directory contains tools for visualizing feature maps from networks trained on different input resolutions to demonstrate that high-resolution inputs enable the network to learn feature maps with more geometric details.

## Files

- **`feature_map_visualizer.py`** - Main script for multi-resolution feature map comparison
- **`color.py`** - Colorization utilities for feature visualization (PCA-based coloring)
- **`features.py`** - Additional feature processing utilities (RGB to HSV/LAB conversion)
- **`README.md`** - This documentation file

## Requirements

- MMDetection framework
- PyTorch
- OpenCV
- Matplotlib
- NumPy
- colorhash (for color utilities)
- plotly (for colorscale utilities)

## Usage

### Quick Start

1. **Check available images and models:**
```bash
# Run from project root directory
python tools/debug/features/run_feature_visualization.py
```

2. **List all available images:**
```bash
PYTHONPATH=/home/<USER>/repos/Mask2Former_v2:/home/<USER>/repos/Mask2Former_v2/tools \
python tools/debug/features/feature_map_visualizer.py --list-images
```

### Basic Usage

**Compare models with different resolutions (COCO format):**
```bash
PYTHONPATH=/home/<USER>/repos/Mask2Former_v2 \
python tools/debug/features/feature_map_visualizer.py \
    --configs work_dirs/hc_rs10_q3_with_floor_2_0808_512x512/mask2former_config.py \
              work_dirs/hc_rs10_q3_with_floor_2_0808_1024x1024/mask2former_config.py \
    --checkpoints work_dirs/hc_rs10_q3_with_floor_2_0808_512x512/best_coco_segm_mAP_85_epoch_600.pth \
                  work_dirs/hc_rs10_q3_with_floor_2_0808_1024x1024/best_coco_segm_mAP_85_epoch_235.pth \
    --image-name "cuishanlantian_res_ff_RS10_11" \
    --data-root /home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0804_1024x1024 \
    --output-dir output/feature_visualizations
```

**Single model visualization:**
```bash
PYTHONPATH=/home/<USER>/repos/Mask2Former_v2 \
python tools/debug/features/feature_map_visualizer.py \
    --configs work_dirs/hc_rs10_q3_with_floor_2_0808_1024x1024/mask2former_config.py \
    --checkpoints work_dirs/hc_rs10_q3_with_floor_2_0808_1024x1024/best_coco_segm_mAP_85_epoch_235.pth \
    --image-name "cuishanlantian_res_ff_RS10_11" \
    --data-root /home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0804_1024x1024 \
    --output-dir output/feature_visualizations
## Data Structure

The tool expects COCO format projection density data with the following structure:

```
/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0804_1024x1024/
├── annotations/
│   ├── train.json          # COCO format annotations
│   ├── val.json
│   └── test.json
├── train/                  # Training density images
│   └── cuishanlantian_res_ff_RS10_11.png
├── val/                    # Validation density images
└── test/                   # Test density images
```

**Key Requirements:**
- **`--data-root`**: Must point to the root directory containing COCO format data
- **Annotation files**: Automatically found in `annotations/` subdirectory
- **Image files**: Located in `train/`, `val/`, or `test/` subdirectories
- **COCO format**: Uses pycocotools to load image information and match filenames

### Advanced Options

```bash
python tools/debug/features/feature_map_visualizer.py \
    --configs config1.py config2.py config3.py \
    --checkpoints checkpoint1.pth checkpoint2.pth checkpoint3.pth \
    --image-name "cuishanlantian_res_ff_RS10_11" \
    --data-root /home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0804_1024x1024 \
    --ann-file /path/to/custom/annotations.json \
    --output-dir ./feature_visualizations \
    --device cuda:1 \
    --max-channels 24
```

## Command Line Arguments

- `--configs`: List of model configuration files (required)
- `--checkpoints`: List of model checkpoint files, must match configs order (required)
- `--image-name`: Name of the projection density image to visualize (e.g., "cuishanlantian_res_ff_RS10_11")
- `--image-path`: Direct path to the image file (alternative to --image-name)
- `--output-dir`: Directory to save visualization results (default: ./feature_visualizations)
- `--device`: Device for model inference (default: cuda:0)
- `--max-channels`: Maximum number of channels to visualize per model (default: 16)
- `--search-dirs`: Additional directories to search for images

## Output

The script generates two types of visualizations:

1. **Feature Comparison** (`{image_name}_feature_comparison.png`):
   - Side-by-side comparison of individual feature channels
   - Shows the highest resolution feature maps from each model
   - Displays selected diverse channels with different activation patterns

2. **Colorized Features** (`{image_name}_colorized_features.png`):
   - PCA-based colorization of feature maps
   - Each pixel color represents the feature vector at that location
   - Enables visualization of spatial feature patterns

## How It Works

1. **Model Loading**: Loads multiple Mask2Former models with different configurations
2. **Feature Extraction**: Uses forward hooks to capture backbone features during inference
3. **Highest Resolution Selection**: Automatically selects the highest resolution feature maps (typically stride 4)
4. **Channel Selection**: Intelligently selects diverse channels based on activation statistics
5. **Visualization**: Creates comparative visualizations showing geometric detail differences

## Expected Results

Networks trained on higher resolution inputs should show:
- More detailed spatial patterns in feature maps
- Better preservation of fine geometric structures
- Richer feature representations at object boundaries
- Enhanced capability to capture small-scale details

## Troubleshooting

### Import Errors
If you encounter import errors, ensure:
- MMDetection is properly installed
- All required Python packages are available
- The script is run from the correct directory

### CUDA Memory Issues
If you run out of GPU memory:
- Use `--device cpu` for CPU inference
- Reduce the number of models being compared
- Use smaller input images

### Image Not Found
If the image search fails:
- Verify the image name is correct
- Use `--image-path` with the full path
- Add custom search directories with `--search-dirs`

## Integration with Existing Codebase

This tool integrates with the existing Mask2Former codebase by:
- Using the same model loading utilities as `simplified_projection_processor.py`
- Leveraging the existing colorization logic from the features directory
- Following the same project structure and conventions
- Supporting the same image formats and naming conventions
