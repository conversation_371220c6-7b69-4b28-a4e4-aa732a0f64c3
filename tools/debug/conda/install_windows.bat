@echo off
REM Windows Conda环境自动安装脚本
REM 用于安装Mask2Former Windows兼容环境

echo ========================================
echo 🪟 Mask2Former Windows环境安装脚本
echo ========================================
echo.

REM 检查conda是否可用
conda --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到conda命令
    echo 请先安装Anaconda或Miniconda
    echo 下载地址: https://www.anaconda.com/products/distribution
    pause
    exit /b 1
)

echo ✅ 检测到conda环境

REM 检查环境文件是否存在
if not exist "mask2former_env_windows.yml" (
    echo ❌ 错误: 未找到mask2former_env_windows.yml文件
    echo 请确保该文件在当前目录中
    pause
    exit /b 1
)

echo ✅ 找到环境配置文件

REM 询问是否继续
echo.
echo 即将创建mask2former conda环境，这可能需要较长时间...
set /p continue="是否继续? (y/n): "
if /i not "%continue%"=="y" (
    echo 安装已取消
    pause
    exit /b 0
)

echo.
echo 📦 开始创建conda环境...
echo.

REM 创建conda环境
conda env create -f mask2former_env_windows.yml
if %errorlevel% neq 0 (
    echo ❌ conda环境创建失败
    echo 请检查网络连接和环境文件
    pause
    exit /b 1
)

echo ✅ 基础环境创建成功

REM 激活环境并安装MMDetection
echo.
echo 📦 安装MMDetection生态系统...
echo.

call conda activate mask2former
if %errorlevel% neq 0 (
    echo ❌ 环境激活失败
    pause
    exit /b 1
)

REM 安装OpenMIM
echo 安装OpenMIM...
pip install openmim
if %errorlevel% neq 0 (
    echo ⚠️ OpenMIM安装失败，跳过MMDetection安装
    goto :test_env
)

REM 使用MIM安装MMCV
echo 安装MMCV...
mim install mmcv
if %errorlevel% neq 0 (
    echo ⚠️ MMCV安装失败，尝试备用方法...
    pip install mmcv
)

REM 安装MMDetection
echo 安装MMDetection...
mim install mmdet
if %errorlevel% neq 0 (
    echo ⚠️ MMDetection安装失败，可能需要手动安装
)

:test_env
echo.
echo 🧪 测试环境安装...
echo.

REM 运行测试脚本
if exist "test_windows_env.py" (
    python test_windows_env.py
) else (
    echo 测试脚本不存在，进行简单验证...
    python -c "import torch; print('PyTorch:', torch.__version__); print('CUDA:', torch.cuda.is_available())"
)

echo.
echo ========================================
echo 🎉 安装完成！
echo ========================================
echo.
echo 使用方法:
echo 1. 激活环境: conda activate mask2former
echo 2. 运行测试: python test_windows_env.py
echo 3. 如有问题请参考: WINDOWS_COMPATIBILITY_GUIDE.md
echo.
echo 常见问题:
echo - 如果CUDA不可用，请检查NVIDIA驱动
echo - 如果MMDetection导入失败，请手动安装
echo - 如需更多帮助，请查看安装指南
echo.

pause
