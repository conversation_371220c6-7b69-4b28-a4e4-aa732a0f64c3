#!/usr/bin/env python3
"""
Windows Conda环境测试脚本
用于验证mask2former_env_windows.yml安装是否成功
"""

import sys
import platform
import warnings
warnings.filterwarnings('ignore')

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f"🔍 {title}")
    print("="*60)

def test_basic_info():
    """测试基础系统信息"""
    print_header("系统信息")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"架构: {platform.architecture()[0]}")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")

def test_core_packages():
    """测试核心科学计算包"""
    print_header("核心科学计算包")
    
    packages = [
        ('numpy', 'np'),
        ('scipy', 'scipy'),
        ('matplotlib', 'plt'),
        ('pandas', 'pd'),
        ('sklearn', 'scikit-learn'),
        ('h5py', 'h5py'),
        ('networkx', 'nx'),
        ('sympy', 'sympy'),
        ('plotly', 'plotly')
    ]
    
    for module, display_name in packages:
        try:
            imported = __import__(module)
            version = getattr(imported, '__version__', 'unknown')
            print(f"✅ {display_name:15}: {version}")
        except ImportError as e:
            print(f"❌ {display_name:15}: 导入失败 - {e}")

def test_pytorch():
    """测试PyTorch生态"""
    print_header("PyTorch生态系统")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        print(f"   CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"   CUDA版本: {torch.version.cuda}")
            print(f"   GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
        else:
            print("   运行在CPU模式")
    except ImportError as e:
        print(f"❌ PyTorch: 导入失败 - {e}")
        return False
    
    try:
        import torchvision
        print(f"✅ TorchVision: {torchvision.__version__}")
    except ImportError as e:
        print(f"❌ TorchVision: 导入失败 - {e}")
    
    try:
        import torchaudio
        print(f"✅ TorchAudio: {torchaudio.__version__}")
    except ImportError as e:
        print(f"❌ TorchAudio: 导入失败 - {e}")
    
    return True

def test_computer_vision():
    """测试计算机视觉包"""
    print_header("计算机视觉包")
    
    try:
        import cv2
        print(f"✅ OpenCV: {cv2.__version__}")
    except ImportError as e:
        print(f"❌ OpenCV: 导入失败 - {e}")
    
    try:
        from PIL import Image
        import PIL
        print(f"✅ Pillow: {PIL.__version__}")
    except ImportError as e:
        print(f"❌ Pillow: 导入失败 - {e}")
    
    try:
        import timm
        print(f"✅ TIMM: {timm.__version__}")
    except ImportError as e:
        print(f"❌ TIMM: 导入失败 - {e}")
    
    try:
        import einops
        print(f"✅ Einops: {einops.__version__}")
    except ImportError as e:
        print(f"❌ Einops: 导入失败 - {e}")

def test_mmdetection():
    """测试MMDetection生态"""
    print_header("MMDetection生态系统")
    
    mm_packages = [
        ('mmcv', 'MMCV'),
        ('mmengine', 'MMEngine'),
        ('mmdet', 'MMDetection'),
        ('mmsegmentation', 'MMSegmentation')
    ]
    
    for module, display_name in mm_packages:
        try:
            imported = __import__(module)
            version = getattr(imported, '__version__', 'unknown')
            print(f"✅ {display_name:15}: {version}")
        except ImportError as e:
            print(f"⚠️ {display_name:15}: 需要手动安装 - {e}")

def test_3d_processing():
    """测试3D处理包"""
    print_header("3D处理和点云")
    
    packages_3d = [
        ('open3d', 'Open3D'),
        ('shapely', 'Shapely'),
        ('plyfile', 'PLYfile'),
        ('laspy', 'LASpy'),
        ('pyquaternion', 'PyQuaternion')
    ]
    
    for module, display_name in packages_3d:
        try:
            imported = __import__(module)
            version = getattr(imported, '__version__', 'unknown')
            print(f"✅ {display_name:15}: {version}")
        except ImportError as e:
            print(f"❌ {display_name:15}: 导入失败 - {e}")

def test_visualization():
    """测试可视化包"""
    print_header("可视化和监控")
    
    viz_packages = [
        ('dash', 'Dash'),
        ('wandb', 'Weights & Biases'),
        ('tensorboard', 'TensorBoard'),
        ('tensorboardX', 'TensorBoardX')
    ]
    
    for module, display_name in viz_packages:
        try:
            imported = __import__(module)
            version = getattr(imported, '__version__', 'unknown')
            print(f"✅ {display_name:15}: {version}")
        except ImportError as e:
            print(f"❌ {display_name:15}: 导入失败 - {e}")

def test_jupyter():
    """测试Jupyter环境"""
    print_header("Jupyter开发环境")
    
    jupyter_packages = [
        ('IPython', 'IPython'),
        ('jupyter_core', 'Jupyter Core'),
        ('jupyter_client', 'Jupyter Client'),
        ('ipykernel', 'IPython Kernel'),
        ('nbformat', 'Notebook Format')
    ]
    
    for module, display_name in jupyter_packages:
        try:
            imported = __import__(module)
            version = getattr(imported, '__version__', 'unknown')
            print(f"✅ {display_name:15}: {version}")
        except ImportError as e:
            print(f"❌ {display_name:15}: 导入失败 - {e}")

def test_advanced_packages():
    """测试高级包（可能需要编译）"""
    print_header("高级包（可能需要手动安装）")
    
    advanced_packages = [
        ('flash_attn', 'Flash Attention'),
        ('torch_cluster', 'PyTorch Cluster'),
        ('torch_geometric', 'PyTorch Geometric'),
        ('torch_scatter', 'PyTorch Scatter'),
        ('torch_sparse', 'PyTorch Sparse'),
        ('triton', 'Triton'),
        ('spconv', 'Sparse Convolution')
    ]
    
    for module, display_name in advanced_packages:
        try:
            imported = __import__(module)
            version = getattr(imported, '__version__', 'unknown')
            print(f"✅ {display_name:15}: {version}")
        except ImportError as e:
            print(f"⚠️ {display_name:15}: 需要手动安装 - {e}")

def generate_report():
    """生成测试报告"""
    print_header("环境测试总结")
    
    # 统计成功/失败的包
    # 这里可以添加更详细的统计逻辑
    
    print("📋 安装建议:")
    print("1. 如果MMDetection包导入失败，请运行:")
    print("   pip install openmim")
    print("   mim install mmcv")
    print("   mim install mmdet")
    print()
    print("2. 如果需要高级包，请参考WINDOWS_COMPATIBILITY_GUIDE.md")
    print()
    print("3. 如果CUDA不可用，请检查:")
    print("   - NVIDIA驱动是否最新")
    print("   - PyTorch CUDA版本是否匹配")
    print()
    print("🎉 测试完成！请查看上述结果确认环境状态。")

def main():
    """主测试函数"""
    print("🧪 开始Windows Conda环境测试...")
    
    test_basic_info()
    test_core_packages()
    pytorch_ok = test_pytorch()
    test_computer_vision()
    test_mmdetection()
    test_3d_processing()
    test_visualization()
    test_jupyter()
    test_advanced_packages()
    generate_report()

if __name__ == "__main__":
    main()
