# Conda environment file for Mask2Former (Windows Compatible)
# Optimized for Windows 10/11 with CUDA support
# Usage: conda env create -f mask2former_env_windows.yml
#        conda activate mask2former

name: mask2former
channels:
  - pytorch
  - nvidia
  - conda-forge
  - defaults
dependencies:
  # —— 基础解释器与科学计算 —— #
  - python=3.11
  - pip
  - numpy=2.2.6
  - scipy=1.15.1
  - matplotlib=3.10.3
  - tqdm=4.65.2
  - pyyaml=6.0.2

  # —— 计算机视觉常用 —— #
  - opencv=4.10.0  # Windows兼容版本
  - pillow=10.4.0

  # —— GPU / PyTorch 栈（Windows CUDA支持）—— #
  - pytorch=2.4.0
  - torchvision=0.19.0
  - torchaudio=2.4.0
  - pytorch-cuda=12.1  # Windows CUDA 12.1支持
  - cudatoolkit  # Windows CUDA工具包

  # —— 其他科学计算包 —— #
  - pandas=2.2.3
  - scikit-learn=1.7.1
  - h5py=3.12.1
  - networkx=3.3
  - sympy=1.13.1
  - plotly=6.2.0

  # —— 开发工具 —— #
  - jupyter
  - ipython=8.25.0
  - setuptools=80.9.0
  - wheel=0.43.0
  - git
  - requests=2.28.2
  - urllib3=1.26.20
  - certifi=2025.1.31
  - click=8.1.7
  - six=1.16.0
  - packaging=24.1
  - jinja2=3.1.4

  - pip:
      # —— 深度学习和计算机视觉核心包 —— #
      - timm==1.0.14
      - einops==0.8.0
      - pycocotools  # Windows版本，不固定版本号
      
      # —— MMDetection生态系统（Windows兼容）—— #
      # 注意：这些包可能需要手动安装或使用预编译wheel
      - mmcv>=2.0.0  # 使用版本范围，避免编译问题
      - mmengine>=0.10.0
      
      # —— 3D处理和点云（Windows兼容版本）—— #
      - open3d==0.19.0  # 有Windows预编译版本
      - shapely==2.1.1  # Windows兼容
      - plyfile==1.1
      - pyquaternion==0.9.9
      
      # —— 可视化和Web应用 —— #
      - dash==3.2.0
      - wandb==0.19.4
      - tensorboard==2.18.0
      - tensorboardX==*******
      
      # —— 基础工具包 —— #
      - addict==2.4.0
      - ConfigArgParse==1.7.1
      - retrying==1.3.4
      - fonttools==4.58.5
      - joblib==1.5.1
      - threadpoolctl==3.6.0
      - typing_extensions==4.12.2
      - importlib_metadata==8.5.0
      - fastjsonschema==2.21.1
      - charset-normalizer==2.0.4
      
      # —— Jupyter和开发工具 —— #
      - ipykernel==6.29.5
      - jupyter_client==8.6.3
      - jupyter_core==5.7.2
      - jupyterlab_widgets==3.0.15
      - matplotlib-inline==0.1.6
      - nest_asyncio==1.6.0
      - nbformat==5.10.4
      - traitlets==5.14.3
      - widgetsnbextension==4.0.14
      
      # —— Windows兼容的其他包 —— #
      - absl-py==2.1.0
      - aiohttp==3.11.11
      - aiosignal==1.3.2
      - annotated-types==0.7.0
      - asttokens==2.0.5
      - astunparse==1.6.3
      - attrs==23.1.0
      - beautifulsoup4==4.12.3
      - blinker==1.9.0
      - cffi==1.16.0
      - chardet==4.0.0
      - colorama==0.4.6  # Windows控制台颜色支持
      - comm==0.2.2
      - contourpy==1.3.2
      - cryptography==42.0.5
      - cycler==0.12.1
      - debugpy==1.8.14
      - decorator==5.1.1
      - executing==0.8.3
      - filelock==3.14.0
      - fire==0.7.0
      - Flask==3.1.1
      - frozendict==2.4.2
      - frozenlist==1.5.0
      - fsspec==2024.6.1
      - ftfy==6.3.1
      - furl==2.1.3
      - gitdb==4.0.12
      - GitPython==3.1.44
      - grpcio==1.62.2
      - huggingface_hub==0.27.1
      - hypothesis==6.108.4
      - idna==3.7
      - itsdangerous==2.2.0
      - jedi==0.19.1
      - jmespath==0.10.0
      - jsonpatch==1.33
      - jsonpointer==2.1
      - jsonschema==4.19.2
      - jsonschema-specifications==2023.7.1
      - kiwisolver==1.4.8
      - lark==1.2.2
      - Markdown==3.6
      - markdown-it-py==3.0.0
      - MarkupSafe==2.1.3
      - mdurl==0.1.2
      - more-itertools==10.1.0
      - mpmath==1.3.0
      - multidict==6.1.0
      - ninja==1.11.1.1  # Windows编译工具
      - ordered-set==4.1.0
      - orderedmultidict==1.0.1
      - pathlib2==2.3.7.post1
      - pexpect==4.8.0  # 可能在Windows上有问题，但通常有替代
      - pkginfo==1.10.0
      - platformdirs==3.10.0
      - pluggy==1.0.0
      - portalocker==3.1.1
      - prettytable==3.16.0
      - prompt-toolkit==3.0.43
      - propcache==0.2.1
      - protobuf==4.25.3
      - psutil==5.9.0
      - ptyprocess==0.7.0  # Windows兼容性可能有问题
      - pure-eval==0.2.2
      - pybind11==2.13.6
      - pycparser==2.21
      - pycryptodome==3.23.0
      - pydantic==2.10.5
      - pydantic_core==2.27.2
      - Pygments==2.15.1
      - PyJWT==2.9.0
      - pyparsing==3.2.1
      - python-dateutil==2.9.0.post0
      - pytz==2023.4
      - pyzmq==26.2.0
      - referencing==0.30.2
      - regex==2024.11.6
      - rich==13.4.2
      - rpds-py==0.10.6
      - ruamel.yaml==0.17.21
      - safetensors==0.5.2
      - sentry-sdk==2.20.0
      - smmap==5.0.2
      - sortedcontainers==2.4.0
      - soupsieve==2.5
      - stack-data==0.2.0
      - tabulate==0.9.0
      - tensorboard_data_server==0.7.0
      - termcolor==2.5.0
      - tomli==2.2.1
      - tornado==6.4.2
      - truststore==0.8.0
      - types-dataclasses==0.6.6
      - tzdata==2024.2
      - wcwidth==0.2.5
      - Werkzeug==3.1.3
      - yarl==1.18.3
      - zipp==3.21.0
      - zstandard==0.22.0
