# 🪟 Windows Conda环境兼容性指南

## 🚨 Linux vs Windows 兼容性问题分析

### ❌ 移除的Linux特定包

| 包名 | 问题 | 解决方案 |
|------|------|----------|
| **anaconda-anon-usage** | Anaconda内部包 | 移除，不影响功能 |
| **archspec** | 系统架构检测 | 移除，conda自动处理 |
| **ccimport** | Linux编译工具 | 移除，Windows不需要 |
| **conda-*系列** | Conda管理包 | 移除，避免冲突 |
| **libarchive-c** | Linux库绑定 | 移除，Windows不兼容 |
| **libmambapy** | Mamba Python绑定 | 移除，可选安装 |
| **lintrunner** | Linux代码检查 | 移除，可手动安装 |
| **menuinst** | Windows菜单管理 | 移除，conda自动处理 |
| **mkl-*系列** | Intel MKL库 | 移除，conda自动安装 |

### ⚠️ 问题包及解决方案

| 包名 | Windows问题 | 解决方案 |
|------|-------------|----------|
| **flash-attn** | 需要CUDA编译 | 移除，或手动安装预编译版 |
| **torch_cluster** | 需要C++编译 | 移除，或使用conda-forge版本 |
| **torch_scatter** | 需要CUDA编译 | 移除，或手动安装 |
| **torch_sparse** | 需要编译 | 移除，或手动安装 |
| **triton** | Linux优先支持 | 移除，Windows支持有限 |
| **cumm-cu120** | Linux CUDA工具 | 移除，使用pytorch-cuda |
| **spconv-cu120** | 稀疏卷积库 | 移除，或手动安装Windows版 |
| **mmdet** | 需要编译 | 改为手动安装或预编译版 |
| **mmsegmentation** | 需要编译 | 改为手动安装 |
| **laspy** | 可能编译问题 | 保留，通常有Windows wheel |
| **pycocotools** | 编译问题 | 改为不固定版本 |

### ✅ Windows优化调整

| 调整项 | 原版本 | Windows版本 | 说明 |
|--------|--------|-------------|------|
| **opencv** | 4.12.0.88 | 4.10.0 | Windows稳定版本 |
| **mmcv** | ==2.2.0 | >=2.0.0 | 版本范围，避免编译 |
| **mmengine** | ==0.10.7 | >=0.10.0 | 版本范围 |
| **pycocotools** | ==2.0.10 | 无版本 | 自动选择兼容版本 |
| **CUDA支持** | 添加cudatoolkit | Windows CUDA支持 |

## 🛠️ Windows安装指南

### 前置要求

1. **Windows 10/11** (64位)
2. **Anaconda/Miniconda** 最新版
3. **NVIDIA GPU** + 最新驱动 (如需GPU支持)
4. **Visual Studio Build Tools** (可选，用于编译)

### 安装步骤

#### 1. 基础环境安装

```bash
# 创建基础环境
conda env create -f mask2former_env_windows.yml

# 激活环境
conda activate mask2former
```

#### 2. 手动安装问题包

```bash
# 激活环境后手动安装MMDetection生态
pip install openmim
mim install mmcv
mim install mmdet
mim install mmsegmentation

# 或者使用预编译版本
pip install mmcv-full -f https://download.openmmlab.com/mmcv/dist/cu121/torch2.4.0/index.html
pip install mmdet
pip install mmsegmentation
```

#### 3. 可选的高级包

```bash
# 如果需要flash-attention (需要编译环境)
pip install flash-attn --no-build-isolation

# 如果需要torch几何包
pip install torch-geometric torch-scatter torch-sparse -f https://data.pyg.org/whl/torch-2.4.0+cu121.html

# 如果需要稀疏卷积
pip install spconv-cu121  # 根据CUDA版本选择
```

### 验证安装

```python
# 验证脚本 - test_windows_env.py
import sys
print(f"Python: {sys.version}")

# 基础包
try:
    import torch, torchvision, numpy as np, cv2
    print(f"✅ PyTorch: {torch.__version__}")
    print(f"✅ TorchVision: {torchvision.__version__}")
    print(f"✅ NumPy: {np.__version__}")
    print(f"✅ OpenCV: {cv2.__version__}")
    print(f"✅ CUDA available: {torch.cuda.is_available()}")
except ImportError as e:
    print(f"❌ 基础包导入失败: {e}")

# MMDetection生态
try:
    import mmcv, mmengine
    print(f"✅ MMCV: {mmcv.__version__}")
    print(f"✅ MMEngine: {mmengine.__version__}")
except ImportError as e:
    print(f"⚠️ MMDetection包需要手动安装: {e}")

# 3D处理
try:
    import open3d as o3d, shapely
    print(f"✅ Open3D: {o3d.__version__}")
    print(f"✅ Shapely: {shapely.__version__}")
except ImportError as e:
    print(f"⚠️ 3D处理包: {e}")

print("\n🎉 环境验证完成!")
```

## 🔧 常见问题解决

### 1. CUDA相关问题

```bash
# 检查CUDA版本
nvidia-smi

# 重新安装PyTorch (根据CUDA版本)
conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia

# 或CPU版本
conda install pytorch torchvision torchaudio cpuonly -c pytorch
```

### 2. 编译错误

```bash
# 安装Visual Studio Build Tools
# 下载: https://visualstudio.microsoft.com/visual-cpp-build-tools/

# 或使用预编译包
pip install --only-binary=all package_name
```

### 3. MMDetection安装失败

```bash
# 方法1: 使用OpenMMLab官方安装器
pip install openmim
mim install mmcv
mim install mmdet

# 方法2: 使用预编译wheel
pip install mmcv -f https://download.openmmlab.com/mmcv/dist/cu121/torch2.4.0/index.html

# 方法3: 从源码安装 (需要编译环境)
git clone https://github.com/open-mmlab/mmdetection.git
cd mmdetection
pip install -e .
```

### 4. 网络问题

```bash
# 使用国内镜像
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/conda-forge/
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/pytorch/

# pip镜像
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
```

## 📋 Windows特定注意事项

### 路径问题
- 使用正斜杠 `/` 或双反斜杠 `\\`
- 避免路径中的中文字符
- 注意Windows路径长度限制

### 权限问题
- 以管理员身份运行命令提示符
- 或使用 `--user` 参数安装包

### 内存限制
- Windows可能需要更多内存
- 考虑分批安装大型包

### 防火墙设置
- 确保Python和conda可以访问网络
- 添加防火墙例外

## 🎯 推荐的Windows安装流程

```bash
# 1. 创建基础环境
conda env create -f mask2former_env_windows.yml

# 2. 激活环境
conda activate mask2former

# 3. 验证基础安装
python -c "import torch; print(torch.__version__); print(torch.cuda.is_available())"

# 4. 手动安装MMDetection
pip install openmim
mim install mmcv
mim install mmdet

# 5. 测试完整环境
python test_windows_env.py

# 6. 根据需要安装额外包
pip install flash-attn  # 如果需要
pip install spconv-cu121  # 如果需要稀疏卷积
```

---

**这个Windows版本移除了所有Linux特定包，优化了版本兼容性，应该可以在Windows上正常安装！** 🎯
