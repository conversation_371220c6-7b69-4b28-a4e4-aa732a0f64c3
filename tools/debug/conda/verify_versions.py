#!/usr/bin/env python3
"""
验证mask2former_env.yml中的版本号是否与pip_list.txt完全匹配
"""

import re
import yaml
from pathlib import Path

def parse_pip_list(pip_list_file):
    """解析pip list文件，提取包名和版本"""
    packages = {}
    with open(pip_list_file, 'r') as f:
        lines = f.readlines()
    
    # 跳过标题行
    for line in lines[2:]:  # 跳过前两行标题
        line = line.strip()
        if not line:
            continue
        
        # 解析格式：Package Version [Editable project location]
        parts = line.split()
        if len(parts) >= 2:
            package_name = parts[0]
            version = parts[1]
            packages[package_name.lower()] = version
    
    return packages

def parse_conda_yml(yml_file):
    """解析conda yml文件，提取pip包的版本"""
    with open(yml_file, 'r') as f:
        data = yaml.safe_load(f)
    
    pip_packages = {}
    conda_packages = {}
    
    # 解析conda包
    for dep in data.get('dependencies', []):
        if isinstance(dep, str) and '=' in dep:
            name, version = dep.split('=', 1)
            conda_packages[name.lower()] = version
        elif isinstance(dep, dict) and 'pip' in dep:
            # 解析pip包
            for pip_pkg in dep['pip']:
                if '==' in pip_pkg:
                    name, version = pip_pkg.split('==', 1)
                    pip_packages[name.lower().replace('-', '_')] = version
    
    return conda_packages, pip_packages

def main():
    """主验证函数"""
    base_dir = Path("tools/debug")
    pip_list_file = base_dir / "conda" / "pip_list.txt"
    yml_file = base_dir / "mask2former_env.yml"
    
    if not pip_list_file.exists():
        print(f"❌ 找不到文件: {pip_list_file}")
        return
    
    if not yml_file.exists():
        print(f"❌ 找不到文件: {yml_file}")
        return
    
    print("🔍 验证版本匹配性...")
    print("=" * 60)
    
    # 解析文件
    actual_packages = parse_pip_list(pip_list_file)
    conda_packages, yml_pip_packages = parse_conda_yml(yml_file)
    
    print(f"📊 统计信息:")
    print(f"  - pip list中的包: {len(actual_packages)}")
    print(f"  - yml中conda包: {len(conda_packages)}")
    print(f"  - yml中pip包: {len(yml_pip_packages)}")
    print()
    
    # 验证关键包
    critical_packages = [
        'torch', 'torchvision', 'torchaudio', 'numpy', 'scipy', 'matplotlib',
        'opencv-python', 'pillow', 'pandas', 'scikit-learn', 'mmcv', 'mmdet',
        'mmengine', 'mmsegmentation', 'timm', 'einops', 'open3d', 'shapely'
    ]
    
    print("🎯 关键包版本验证:")
    print("-" * 60)
    
    all_correct = True
    
    for pkg in critical_packages:
        pkg_key = pkg.lower().replace('-', '_')
        
        if pkg_key in actual_packages:
            actual_version = actual_packages[pkg_key]
            
            # 检查是否在conda包中
            if pkg.lower() in conda_packages:
                yml_version = conda_packages[pkg.lower()]
                source = "conda"
            elif pkg_key in yml_pip_packages:
                yml_version = yml_pip_packages[pkg_key]
                source = "pip"
            else:
                print(f"❌ {pkg:20}: 在yml中缺失")
                all_correct = False
                continue
            
            if actual_version == yml_version:
                print(f"✅ {pkg:20}: {actual_version:15} ({source})")
            else:
                print(f"❌ {pkg:20}: 实际={actual_version:10} yml={yml_version:10} ({source})")
                all_correct = False
        else:
            print(f"⚠️  {pkg:20}: 在pip list中未找到")
    
    print()
    print("🔍 检查所有pip包的版本匹配:")
    print("-" * 60)
    
    mismatched = []
    missing_in_yml = []
    
    for pkg_name, actual_version in actual_packages.items():
        # 标准化包名
        pkg_key = pkg_name.replace('-', '_')
        
        if pkg_key in yml_pip_packages:
            yml_version = yml_pip_packages[pkg_key]
            if actual_version != yml_version:
                mismatched.append((pkg_name, actual_version, yml_version))
        elif pkg_name in conda_packages:
            # 在conda包中，跳过
            continue
        else:
            missing_in_yml.append((pkg_name, actual_version))
    
    if mismatched:
        print(f"❌ 版本不匹配的包 ({len(mismatched)}个):")
        for pkg, actual, yml in mismatched[:10]:  # 只显示前10个
            print(f"   {pkg:25}: 实际={actual:10} yml={yml}")
        if len(mismatched) > 10:
            print(f"   ... 还有 {len(mismatched) - 10} 个不匹配的包")
        all_correct = False
    
    if missing_in_yml:
        print(f"⚠️  yml中缺失的包 ({len(missing_in_yml)}个):")
        for pkg, version in missing_in_yml[:10]:  # 只显示前10个
            print(f"   {pkg:25}: {version}")
        if len(missing_in_yml) > 10:
            print(f"   ... 还有 {len(missing_in_yml) - 10} 个缺失的包")
    
    print()
    print("=" * 60)
    if all_correct and not mismatched and not missing_in_yml:
        print("🎉 所有版本完全匹配！环境文件正确！")
    else:
        print("⚠️  发现版本不匹配或缺失的包，需要修正")
        print(f"   - 不匹配: {len(mismatched)}个")
        print(f"   - 缺失: {len(missing_in_yml)}个")

if __name__ == "__main__":
    main()
