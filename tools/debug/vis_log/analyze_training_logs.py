#!/usr/bin/env python3
"""
Training Log Analysis Script for Mask2Former
分析1024x1024训练中的梯度爆炸问题

Author: Generated for gradient explosion analysis
Date: 2025-08-22
"""

import argparse
import sys
from pathlib import Path
import pandas as pd
import numpy as np

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from training_log_parser import TrainingLogParser
from loss_gradient_visualizer import LossGradientVisualizer


def analyze_single_log(log_file: str, output_dir: str = None) -> dict:
    """
    分析单个训练日志文件
    
    Args:
        log_file: 日志文件路径
        output_dir: 输出目录
        
    Returns:
        分析结果字典
    """
    print(f"\n{'='*60}")
    print(f"分析训练日志: {log_file}")
    print(f"{'='*60}")
    
    # 解析日志
    parser = TrainingLogParser(log_file)
    df = parser.parse_log()
    
    if df.empty:
        print("错误: 无法解析日志文件或日志为空")
        return {}
    
    # 分析训练稳定性
    analysis = parser.analyze_training_stability(df)
    
    # 打印分析结果
    print(f"\n=== 训练基本信息 ===")
    print(f"总迭代次数: {analysis['total_iterations']}")
    print(f"训练轮数: {analysis['epochs']}")
    print(f"训练时间范围: {df['timestamp'].min()} - {df['timestamp'].max()}")
    
    print(f"\n=== 梯度范数统计 ===")
    grad_stats = analysis['grad_norm_stats']
    print(f"平均值: {grad_stats['mean']:.2f}")
    print(f"标准差: {grad_stats['std']:.2f}")
    print(f"最小值: {grad_stats['min']:.2f}")
    print(f"最大值: {grad_stats['max']:.2f}")
    print(f"中位数: {grad_stats['median']:.2f}")
    print(f"95%分位数: {grad_stats['q95']:.2f}")
    print(f"99%分位数: {grad_stats['q99']:.2f}")
    
    print(f"\n=== Loss统计 ===")
    loss_stats = analysis['loss_stats']
    print(f"平均值: {loss_stats['mean']:.4f}")
    print(f"标准差: {loss_stats['std']:.4f}")
    print(f"最小值: {loss_stats['min']:.4f}")
    print(f"最大值: {loss_stats['max']:.4f}")
    print(f"中位数: {loss_stats['median']:.4f}")
    
    print(f"\n=== 梯度爆炸事件 ===")
    print(f"中等爆炸 (>1000): {analysis['gradient_explosion_count']}")
    print(f"严重爆炸 (>5000): {analysis['severe_explosion_count']}")
    print(f"极端爆炸 (>10000): {analysis['extreme_explosion_count']}")
    
    # 识别最严重的梯度爆炸事件
    explosion_events = parser.get_gradient_explosion_events(df, threshold=1000.0)
    if not explosion_events.empty:
        print(f"\n=== 前10个最严重的梯度爆炸事件 ===")
        top_events = explosion_events.head(10)
        for idx, (_, event) in enumerate(top_events.iterrows(), 1):
            print(f"{idx:2d}. Epoch {event['epoch']:3d}, Iter {event['iter']:3d}: "
                  f"grad_norm={event['grad_norm']:8.2f}, loss={event['loss']:6.2f}")
    
    # 生成可视化图表
    if output_dir is None:
        output_dir = f"tools/debug/vis_log/plots/{Path(log_file).stem}"
    
    visualizer = LossGradientVisualizer(output_dir)
    
    print(f"\n=== 生成可视化图表 ===")
    
    # 1. 梯度范数和loss时间序列
    log_name = Path(log_file).stem
    title_suffix = f" - {log_name}"
    visualizer.plot_gradient_norms_and_loss(df, title_suffix)
    
    # 2. Loss组件分解
    visualizer.plot_loss_components(df, title_suffix)
    
    # 3. 梯度爆炸分析
    visualizer.plot_gradient_explosion_analysis(df, threshold=1000.0)
    
    # 4. Decoder层分析（如果有数据）
    visualizer.plot_decoder_layer_analysis(df)
    
    print(f"\n所有图表已保存到: {output_dir}")
    
    return {
        'analysis': analysis,
        'dataframe': df,
        'explosion_events': explosion_events,
        'output_dir': output_dir
    }


def compare_multiple_logs(log_files: list, output_dir: str = "tools/debug/vis_log/plots/comparison"):
    """
    比较多个训练日志
    
    Args:
        log_files: 日志文件列表
        output_dir: 输出目录
    """
    print(f"\n{'='*60}")
    print(f"比较多个训练日志")
    print(f"{'='*60}")
    
    results = {}
    all_dfs = []
    
    for log_file in log_files:
        result = analyze_single_log(log_file, None)
        if result:
            log_name = Path(log_file).stem
            results[log_name] = result
            
            # 为比较添加标识
            df = result['dataframe'].copy()
            df['log_name'] = log_name
            all_dfs.append(df)
    
    if len(all_dfs) < 2:
        print("需要至少2个有效的日志文件进行比较")
        return
    
    # 合并所有数据
    combined_df = pd.concat(all_dfs, ignore_index=True)
    
    # 生成比较图表
    visualizer = LossGradientVisualizer(output_dir)
    
    print(f"\n=== 生成比较图表 ===")
    
    # 这里可以添加比较可视化的代码
    # 例如：不同训练的梯度范数对比、loss对比等
    
    print(f"比较结果已保存到: {output_dir}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="分析Mask2Former训练日志中的梯度爆炸问题")
    parser.add_argument("log_files", nargs="+", help="训练日志文件路径")
    parser.add_argument("--output-dir", "-o", help="输出目录")
    parser.add_argument("--compare", action="store_true", help="比较多个日志文件")
    parser.add_argument("--threshold", type=float, default=1000.0, help="梯度爆炸阈值")
    
    args = parser.parse_args()
    
    # 验证日志文件存在
    valid_log_files = []
    for log_file in args.log_files:
        if Path(log_file).exists():
            valid_log_files.append(log_file)
        else:
            print(f"警告: 日志文件不存在: {log_file}")
    
    if not valid_log_files:
        print("错误: 没有找到有效的日志文件")
        return
    
    if args.compare and len(valid_log_files) > 1:
        compare_multiple_logs(valid_log_files, args.output_dir)
    else:
        for log_file in valid_log_files:
            analyze_single_log(log_file, args.output_dir)


if __name__ == "__main__":
    # 如果直接运行，分析默认的1024x1024日志
    if len(sys.argv) == 1:
        # 默认分析梯度爆炸日志
        default_log = "work_dirs/hc_rs10_q2_wo_floor_2_0804_1024x1024/20250805_173142/20250805_173142.log"
        if Path(default_log).exists():
            print("使用默认日志文件进行分析...")
            analyze_single_log(default_log)
        else:
            print(f"默认日志文件不存在: {default_log}")
            print("请指定日志文件路径")
            print("用法: python analyze_training_logs.py <log_file1> [log_file2] ...")
    else:
        main()
