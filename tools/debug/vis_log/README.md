# Training Log Visualization Tools

用于分析Mask2Former训练日志中的loss和梯度变化，特别关注1024x1024分辨率训练中的梯度爆炸问题。

## 文件说明

### 核心模块

1. **`training_log_parser.py`** - 训练日志解析器
   - 解析MMEngine格式的训练日志
   - 提取梯度范数、loss组件、decoder层数据
   - 识别梯度爆炸事件
   - 计算训练稳定性统计

2. **`loss_gradient_visualizer.py`** - 可视化工具
   - 生成梯度范数和loss时间序列图
   - 绘制loss组件分解图
   - 梯度爆炸事件分析图
   - Decoder层loss分析图

3. **`analyze_training_logs.py`** - 主分析脚本
   - 完整的日志分析流程
   - 支持单个或多个日志文件比较
   - 生成详细的分析报告和可视化图表

4. **`run_analysis.py`** - 快速运行脚本
   - 自动查找1024x1024训练日志
   - 一键运行完整分析

## 使用方法

### 方法1: 快速分析（推荐）

```bash
cd tools/debug/vis_log
python run_analysis.py
```

这会自动查找并分析所有1024x1024训练日志文件。

### 方法2: 分析指定日志文件

```bash
cd tools/debug/vis_log
python analyze_training_logs.py path/to/your/logfile.log
```

### 方法3: 比较多个日志文件

```bash
cd tools/debug/vis_log
python analyze_training_logs.py --compare log1.log log2.log log3.log
```

### 方法4: 自定义分析

```python
from training_log_parser import TrainingLogParser
from loss_gradient_visualizer import LossGradientVisualizer

# 解析日志
parser = TrainingLogParser("your_log_file.log")
df = parser.parse_log()

# 分析稳定性
analysis = parser.analyze_training_stability(df)

# 生成可视化
visualizer = LossGradientVisualizer("output_dir")
visualizer.plot_gradient_norms_and_loss(df)
visualizer.plot_gradient_explosion_analysis(df)
```

## 输出说明

### 分析报告

运行分析后会在终端输出详细报告，包括：

- **训练基本信息**: 迭代次数、轮数、时间范围
- **梯度范数统计**: 均值、标准差、分位数等
- **Loss统计**: 各种loss组件的统计信息
- **梯度爆炸事件**: 不同严重程度的爆炸事件计数
- **最严重事件列表**: 前10个最严重的梯度爆炸事件

### 可视化图表

生成的图表保存在 `tools/debug/vis_log/plots/` 目录下：

1. **`gradient_norms_and_loss.png`** - 梯度范数和总loss时间序列
   - 上图：梯度范数变化（标记爆炸点）
   - 下图：总loss变化
   - 包含epoch分割线

2. **`loss_components.png`** - Loss组件分解图
   - 显示loss_cls、loss_mask、loss_dice的变化
   - 对比总loss趋势

3. **`gradient_explosion_analysis.png`** - 梯度爆炸分析（四合一图）
   - 梯度范数分布直方图
   - 爆炸事件时间分布
   - 梯度范数vs Loss散点图
   - 梯度范数移动平均

4. **`decoder_layer_analysis.png`** - Decoder层分析
   - 各decoder层的loss变化
   - 按loss类型分组显示

## 梯度爆炸阈值

默认使用以下阈值定义梯度爆炸：
- **中等爆炸**: grad_norm > 1000
- **严重爆炸**: grad_norm > 5000  
- **极端爆炸**: grad_norm > 10000

可以通过 `--threshold` 参数自定义阈值。

## 已知的1024x1024训练问题

根据分析发现的问题：

1. **梯度爆炸严重**: 最大梯度范数超过14000
2. **训练不稳定**: 梯度范数波动极大
3. **Loss震荡**: 伴随梯度爆炸出现loss剧烈波动
4. **早期发生**: 梯度爆炸主要在训练早期（前20个epoch）

## 依赖包

```bash
pip install matplotlib seaborn pandas numpy
```

## 注意事项

1. 确保日志文件路径正确
2. 日志文件需要包含完整的训练信息（梯度范数、loss等）
3. 生成的图片为高分辨率PNG格式
4. 大型日志文件可能需要较长解析时间

## 故障排除

### 常见问题

1. **"没有找到训练数据"**
   - 检查日志文件格式是否正确
   - 确认日志包含 `grad_norm` 和 `loss` 信息

2. **"日志文件不存在"**
   - 检查文件路径是否正确
   - 确认相对路径是从项目根目录开始

3. **图表显示异常**
   - 检查是否安装了中文字体
   - 尝试更新matplotlib版本

### 调试模式

在脚本中添加调试信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 更新历史

- 2025-08-22: 初始版本，支持1024x1024训练日志分析
- 重新创建了之前丢失的梯度监控和loss分析工具
