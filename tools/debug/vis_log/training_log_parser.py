#!/usr/bin/env python3
"""
Training Log Parser for Mask2Former
解析训练日志文件，提取loss和梯度信息用于可视化分析

Author: Generated for gradient explosion analysis
Date: 2025-08-22
"""

import re
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import numpy as np
from datetime import datetime


class TrainingLogParser:
    """训练日志解析器"""
    
    def __init__(self, log_file: str):
        """
        初始化日志解析器
        
        Args:
            log_file: 训练日志文件路径
        """
        self.log_file = Path(log_file)
        self.data = []
        
        # 正则表达式模式
        self.patterns = {
            'training_line': re.compile(
                r'(\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}).*?'
                r'Epoch\(train\)\s+\[(\d+)\]\[(\d+)/(\d+)\].*?'
                r'grad_norm:\s+([\d.]+).*?'
                r'loss:\s+([\d.]+).*?'
                r'loss_cls:\s+([\d.]+).*?'
                r'loss_mask:\s+([\d.]+).*?'
                r'loss_dice:\s+([\d.]+)'
            ),
            'validation_line': re.compile(
                r'(\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}).*?'
                r'Epoch\(val\)\s+\[(\d+)\]\[(\d+)/(\d+)\].*?'
                r'loss:\s+([\d.]+)'
            ),
            'decoder_losses': re.compile(
                r'd(\d+)\.loss_cls:\s+([\d.]+).*?'
                r'd\1\.loss_mask:\s+([\d.]+).*?'
                r'd\1\.loss_dice:\s+([\d.]+)'
            )
        }
    
    def parse_log(self) -> pd.DataFrame:
        """
        解析训练日志文件
        
        Returns:
            包含训练数据的DataFrame
        """
        print(f"解析日志文件: {self.log_file}")
        
        if not self.log_file.exists():
            raise FileNotFoundError(f"日志文件不存在: {self.log_file}")
        
        with open(self.log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"总共 {len(lines)} 行日志")
        
        for line_num, line in enumerate(lines, 1):
            self._parse_training_line(line, line_num)
        
        if not self.data:
            print("警告: 没有找到训练数据")
            return pd.DataFrame()
        
        df = pd.DataFrame(self.data)
        print(f"成功解析 {len(df)} 条训练记录")
        
        # 数据类型转换
        numeric_cols = ['epoch', 'iter', 'total_iters', 'grad_norm', 'loss', 
                       'loss_cls', 'loss_mask', 'loss_dice']
        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 时间戳转换
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        return df
    
    def _parse_training_line(self, line: str, line_num: int):
        """解析训练行"""
        match = self.patterns['training_line'].search(line)
        if match:
            timestamp, epoch, iter_num, total_iters, grad_norm, loss, loss_cls, loss_mask, loss_dice = match.groups()
            
            record = {
                'line_num': line_num,
                'timestamp': timestamp,
                'epoch': int(epoch),
                'iter': int(iter_num),
                'total_iters': int(total_iters),
                'grad_norm': float(grad_norm),
                'loss': float(loss),
                'loss_cls': float(loss_cls),
                'loss_mask': float(loss_mask),
                'loss_dice': float(loss_dice)
            }
            
            # 解析decoder层的loss
            decoder_matches = self.patterns['decoder_losses'].findall(line)
            for decoder_id, d_loss_cls, d_loss_mask, d_loss_dice in decoder_matches:
                record[f'd{decoder_id}_loss_cls'] = float(d_loss_cls)
                record[f'd{decoder_id}_loss_mask'] = float(d_loss_mask)
                record[f'd{decoder_id}_loss_dice'] = float(d_loss_dice)
            
            self.data.append(record)
    
    def get_gradient_explosion_events(self, df: pd.DataFrame, threshold: float = 1000.0) -> pd.DataFrame:
        """
        识别梯度爆炸事件
        
        Args:
            df: 训练数据DataFrame
            threshold: 梯度范数阈值
            
        Returns:
            梯度爆炸事件的DataFrame
        """
        explosion_events = df[df['grad_norm'] > threshold].copy()
        explosion_events = explosion_events.sort_values('grad_norm', ascending=False)
        
        print(f"发现 {len(explosion_events)} 个梯度爆炸事件 (阈值: {threshold})")
        if len(explosion_events) > 0:
            print(f"最大梯度范数: {explosion_events['grad_norm'].max():.2f}")
            print(f"平均梯度范数: {explosion_events['grad_norm'].mean():.2f}")
        
        return explosion_events
    
    def analyze_training_stability(self, df: pd.DataFrame) -> Dict:
        """
        分析训练稳定性
        
        Args:
            df: 训练数据DataFrame
            
        Returns:
            稳定性分析结果
        """
        if df.empty:
            return {}
        
        analysis = {
            'total_iterations': len(df),
            'epochs': df['epoch'].nunique(),
            'grad_norm_stats': {
                'mean': df['grad_norm'].mean(),
                'std': df['grad_norm'].std(),
                'min': df['grad_norm'].min(),
                'max': df['grad_norm'].max(),
                'median': df['grad_norm'].median(),
                'q95': df['grad_norm'].quantile(0.95),
                'q99': df['grad_norm'].quantile(0.99)
            },
            'loss_stats': {
                'mean': df['loss'].mean(),
                'std': df['loss'].std(),
                'min': df['loss'].min(),
                'max': df['loss'].max(),
                'median': df['loss'].median()
            },
            'gradient_explosion_count': len(df[df['grad_norm'] > 1000]),
            'severe_explosion_count': len(df[df['grad_norm'] > 5000]),
            'extreme_explosion_count': len(df[df['grad_norm'] > 10000])
        }
        
        return analysis


def main():
    """主函数 - 示例用法"""
    # 示例：解析1024x1024训练日志
    log_file = "work_dirs/hc_rs10_q2_wo_floor_2_0804_1024x1024/20250805_173142/20250805_173142.log"
    
    parser = TrainingLogParser(log_file)
    df = parser.parse_log()
    
    if not df.empty:
        # 分析训练稳定性
        analysis = parser.analyze_training_stability(df)
        print("\n=== 训练稳定性分析 ===")
        print(f"总迭代次数: {analysis['total_iterations']}")
        print(f"训练轮数: {analysis['epochs']}")
        print(f"梯度范数统计:")
        print(f"  平均值: {analysis['grad_norm_stats']['mean']:.2f}")
        print(f"  标准差: {analysis['grad_norm_stats']['std']:.2f}")
        print(f"  最大值: {analysis['grad_norm_stats']['max']:.2f}")
        print(f"  95%分位数: {analysis['grad_norm_stats']['q95']:.2f}")
        print(f"梯度爆炸事件:")
        print(f"  中等爆炸 (>1000): {analysis['gradient_explosion_count']}")
        print(f"  严重爆炸 (>5000): {analysis['severe_explosion_count']}")
        print(f"  极端爆炸 (>10000): {analysis['extreme_explosion_count']}")
        
        # 识别梯度爆炸事件
        explosion_events = parser.get_gradient_explosion_events(df, threshold=1000.0)
        if not explosion_events.empty:
            print(f"\n=== 前5个最严重的梯度爆炸事件 ===")
            top_events = explosion_events.head(5)
            for _, event in top_events.iterrows():
                print(f"Epoch {event['epoch']}, Iter {event['iter']}: "
                      f"grad_norm={event['grad_norm']:.2f}, loss={event['loss']:.2f}")


if __name__ == "__main__":
    main()
