#!/usr/bin/env python3
"""
Quick Analysis Runner for 1024x1024 Training Logs
快速运行1024x1024训练日志的梯度爆炸分析

Author: Generated for gradient explosion analysis
Date: 2025-08-22
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from analyze_training_logs import analyze_single_log


def main():
    """运行分析"""
    print("1024x1024训练梯度爆炸分析工具")
    print("="*50)
    
    # 可能的日志文件路径（相对于项目根目录）
    possible_logs = [
        "../../../work_dirs/hc_rs10_q2_wo_floor_2_0804_1024x1024/20250805_173142/20250805_173142.log",
        "../../../work_dirs/hc_rs10_q2_wo_floor_2_0804_1024x1024/20250806_105144/20250806_105144.log",
        "../../../work_dirs/hc_rs10_q2_wo_floor_2_0804_1024x1024/20250805_173038/20250805_173038.log"
    ]
    
    found_logs = []
    for log_path in possible_logs:
        if Path(log_path).exists():
            found_logs.append(log_path)
            print(f"找到日志文件: {log_path}")
    
    if not found_logs:
        print("没有找到1024x1024训练日志文件")
        print("请检查以下路径是否存在:")
        for log_path in possible_logs:
            print(f"  - {log_path}")
        return
    
    # 分析每个找到的日志文件
    for log_file in found_logs:
        try:
            result = analyze_single_log(log_file)
            if result:
                print(f"\n✅ 成功分析: {log_file}")
                print(f"   图表保存在: {result['output_dir']}")
            else:
                print(f"\n❌ 分析失败: {log_file}")
        except Exception as e:
            print(f"\n❌ 分析出错: {log_file}")
            print(f"   错误信息: {str(e)}")
    
    print(f"\n分析完成！")
    print(f"所有图表保存在 tools/debug/vis_log/plots/ 目录下")


if __name__ == "__main__":
    main()
