#!/usr/bin/env python3
"""
Loss and Gradient Visualizer for Mask2Former Training
可视化训练过程中的loss和梯度变化，特别关注梯度爆炸问题

Author: Generated for gradient explosion analysis
Date: 2025-08-22
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
sns.set_palette("husl")


class LossGradientVisualizer:
    """Loss和梯度可视化器"""
    
    def __init__(self, output_dir: str = "tools/debug/vis_log/plots"):
        """
        初始化可视化器
        
        Args:
            output_dir: 输出图片的目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 颜色配置
        self.colors = {
            'grad_norm': '#e74c3c',
            'loss': '#3498db',
            'loss_cls': '#f39c12',
            'loss_mask': '#2ecc71',
            'loss_dice': '#9b59b6',
            'explosion': '#ff0000'
        }
    
    def plot_gradient_norms_and_loss(self, df: pd.DataFrame, title_suffix: str = "") -> str:
        """
        绘制梯度范数和loss的时间序列图
        
        Args:
            df: 训练数据DataFrame
            title_suffix: 标题后缀
            
        Returns:
            保存的图片路径
        """
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
        
        # 创建全局迭代索引
        df = df.copy()
        df['global_iter'] = range(len(df))
        
        # 上图：梯度范数
        ax1.plot(df['global_iter'], df['grad_norm'], 
                color=self.colors['grad_norm'], linewidth=1, alpha=0.7)
        ax1.set_ylabel('Gradient Norm', fontsize=12)
        ax1.set_title(f'Gradient Norms Over Training{title_suffix}', fontsize=14, fontweight='bold')
        ax1.grid(True, alpha=0.3)
        
        # 标记梯度爆炸点
        explosion_mask = df['grad_norm'] > 1000
        if explosion_mask.any():
            ax1.scatter(df.loc[explosion_mask, 'global_iter'], 
                       df.loc[explosion_mask, 'grad_norm'],
                       color=self.colors['explosion'], s=30, alpha=0.8, 
                       label=f'Gradient Explosion (>{1000})')
            ax1.legend()
        
        # 设置y轴为对数刻度（如果有很大的值）
        if df['grad_norm'].max() > 1000:
            ax1.set_yscale('log')
            ax1.set_ylabel('Gradient Norm (log scale)', fontsize=12)
        
        # 下图：总loss
        ax2.plot(df['global_iter'], df['loss'], 
                color=self.colors['loss'], linewidth=1, alpha=0.8)
        ax2.set_xlabel('Training Iteration', fontsize=12)
        ax2.set_ylabel('Total Loss', fontsize=12)
        ax2.set_title(f'Total Loss Over Training{title_suffix}', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3)
        
        # 添加epoch分割线
        if 'epoch' in df.columns:
            epoch_changes = df[df['epoch'] != df['epoch'].shift()]['global_iter']
            for epoch_start in epoch_changes:
                ax1.axvline(x=epoch_start, color='gray', linestyle='--', alpha=0.5)
                ax2.axvline(x=epoch_start, color='gray', linestyle='--', alpha=0.5)
        
        plt.tight_layout()
        
        # 保存图片
        filename = f"gradient_norms_and_loss{title_suffix.replace(' ', '_').replace(':', '')}.png"
        filepath = self.output_dir / filename
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"保存梯度范数和loss图表: {filepath}")
        return str(filepath)
    
    def plot_loss_components(self, df: pd.DataFrame, title_suffix: str = "") -> str:
        """
        绘制loss组件分解图
        
        Args:
            df: 训练数据DataFrame
            title_suffix: 标题后缀
            
        Returns:
            保存的图片路径
        """
        fig, ax = plt.subplots(figsize=(15, 8))
        
        df = df.copy()
        df['global_iter'] = range(len(df))
        
        # 绘制各个loss组件
        loss_components = ['loss_cls', 'loss_mask', 'loss_dice']
        for component in loss_components:
            if component in df.columns:
                ax.plot(df['global_iter'], df[component], 
                       label=component.replace('_', ' ').title(),
                       color=self.colors[component], linewidth=1, alpha=0.8)
        
        # 绘制总loss
        ax.plot(df['global_iter'], df['loss'], 
               label='Total Loss', color=self.colors['loss'], 
               linewidth=2, alpha=0.9)
        
        ax.set_xlabel('Training Iteration', fontsize=12)
        ax.set_ylabel('Loss Value', fontsize=12)
        ax.set_title(f'Loss Components Over Training{title_suffix}', fontsize=14, fontweight='bold')
        ax.legend(loc='upper right')
        ax.grid(True, alpha=0.3)
        
        # 添加epoch分割线
        if 'epoch' in df.columns:
            epoch_changes = df[df['epoch'] != df['epoch'].shift()]['global_iter']
            for epoch_start in epoch_changes:
                ax.axvline(x=epoch_start, color='gray', linestyle='--', alpha=0.5)
        
        plt.tight_layout()
        
        # 保存图片
        filename = f"loss_components{title_suffix.replace(' ', '_').replace(':', '')}.png"
        filepath = self.output_dir / filename
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"保存loss组件图表: {filepath}")
        return str(filepath)

    def plot_gradient_explosion_analysis(self, df: pd.DataFrame, threshold: float = 1000.0) -> str:
        """
        绘制梯度爆炸分析图

        Args:
            df: 训练数据DataFrame
            threshold: 梯度爆炸阈值

        Returns:
            保存的图片路径
        """
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # 1. 梯度范数分布直方图
        ax1.hist(df['grad_norm'], bins=50, alpha=0.7, color=self.colors['grad_norm'], edgecolor='black')
        ax1.axvline(threshold, color=self.colors['explosion'], linestyle='--',
                   label=f'Explosion Threshold ({threshold})')
        ax1.set_xlabel('Gradient Norm')
        ax1.set_ylabel('Frequency')
        ax1.set_title('Gradient Norm Distribution')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 梯度爆炸事件时间分布
        explosion_events = df[df['grad_norm'] > threshold]
        if not explosion_events.empty:
            ax2.scatter(explosion_events['epoch'], explosion_events['grad_norm'],
                       color=self.colors['explosion'], alpha=0.7, s=50)
            ax2.set_xlabel('Epoch')
            ax2.set_ylabel('Gradient Norm')
            ax2.set_title(f'Gradient Explosion Events (>{threshold})')
            ax2.grid(True, alpha=0.3)
        else:
            ax2.text(0.5, 0.5, 'No Gradient Explosion Events',
                    transform=ax2.transAxes, ha='center', va='center', fontsize=14)
            ax2.set_title('Gradient Explosion Events')

        # 3. 梯度范数 vs Loss 散点图
        scatter = ax3.scatter(df['grad_norm'], df['loss'],
                            c=df.index, cmap='viridis', alpha=0.6, s=20)
        ax3.set_xlabel('Gradient Norm')
        ax3.set_ylabel('Total Loss')
        ax3.set_title('Gradient Norm vs Loss')
        ax3.grid(True, alpha=0.3)
        plt.colorbar(scatter, ax=ax3, label='Training Progress')

        # 4. 梯度范数移动平均
        window_size = min(100, len(df) // 10)
        if window_size > 1:
            df_copy = df.copy()
            df_copy['grad_norm_ma'] = df_copy['grad_norm'].rolling(window=window_size).mean()
            ax4.plot(df_copy.index, df_copy['grad_norm'], alpha=0.3, color='lightgray', label='Raw')
            ax4.plot(df_copy.index, df_copy['grad_norm_ma'],
                    color=self.colors['grad_norm'], linewidth=2, label=f'MA({window_size})')
            ax4.axhline(threshold, color=self.colors['explosion'], linestyle='--',
                       label=f'Threshold ({threshold})')
            ax4.set_xlabel('Training Iteration')
            ax4.set_ylabel('Gradient Norm')
            ax4.set_title('Gradient Norm Moving Average')
            ax4.legend()
            ax4.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图片
        filename = "gradient_explosion_analysis.png"
        filepath = self.output_dir / filename
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"保存梯度爆炸分析图表: {filepath}")
        return str(filepath)

    def plot_decoder_layer_analysis(self, df: pd.DataFrame) -> Optional[str]:
        """
        绘制decoder层loss分析图

        Args:
            df: 训练数据DataFrame

        Returns:
            保存的图片路径，如果没有decoder数据则返回None
        """
        # 查找decoder层数据
        decoder_cols = [col for col in df.columns if col.startswith('d') and 'loss' in col]
        if not decoder_cols:
            print("没有找到decoder层数据")
            return None

        # 提取decoder层编号
        decoder_layers = set()
        for col in decoder_cols:
            layer_match = col.split('_')[0][1:]  # 提取d0, d1等中的数字
            if layer_match.isdigit():
                decoder_layers.add(int(layer_match))

        decoder_layers = sorted(decoder_layers)
        if not decoder_layers:
            return None

        fig, axes = plt.subplots(len(decoder_layers), 3, figsize=(18, 4*len(decoder_layers)))
        if len(decoder_layers) == 1:
            axes = axes.reshape(1, -1)

        df = df.copy()
        df['global_iter'] = range(len(df))

        loss_types = ['cls', 'mask', 'dice']
        colors = [self.colors['loss_cls'], self.colors['loss_mask'], self.colors['loss_dice']]

        for i, layer in enumerate(decoder_layers):
            for j, loss_type in enumerate(loss_types):
                col_name = f'd{layer}_loss_{loss_type}'
                if col_name in df.columns:
                    axes[i, j].plot(df['global_iter'], df[col_name],
                                   color=colors[j], linewidth=1, alpha=0.8)
                    axes[i, j].set_title(f'Decoder Layer {layer} - {loss_type.title()} Loss')
                    axes[i, j].set_xlabel('Training Iteration')
                    axes[i, j].set_ylabel('Loss Value')
                    axes[i, j].grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图片
        filename = "decoder_layer_analysis.png"
        filepath = self.output_dir / filename
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"保存decoder层分析图表: {filepath}")
        return str(filepath)
