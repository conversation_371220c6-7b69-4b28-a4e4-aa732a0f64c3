"""
This code is an adaptation that uses Structured 3D for the code base.

Reference: https://github.com/bertjiazheng/Structured3D
"""

import numpy as np
from shapely.geometry import Polygon
import os
import json
import sys
import cv2

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
from configs.mask2former_config import set_img_size

sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from common_utils import resort_corners

type2id = {'living room': 0, 'kitchen': 1, 'bedroom': 2, 'bathroom': 3, 'balcony': 4, 'corridor': 5,
            'dining room': 6, 'study': 7, 'studio': 8, 'store room': 9, 'garden': 10, 'laundry room': 11,
            'office': 12, 'basement': 13, 'garage': 14, 'undefined': 15, 'door': 16, 'window': 17}



def generate_density(point_cloud, width=set_img_size, height=set_img_size):

    ps = point_cloud * -1
    ps[:,0] *= -1
    ps[:,1] *= -1

    image_res = np.array((width, height))

    max_coords = np.max(ps, axis=0)
    min_coords = np.min(ps, axis=0)
    max_m_min = max_coords - min_coords

    max_coords = max_coords + 0.1 * max_m_min
    min_coords = min_coords - 0.1 * max_m_min

    normalization_dict = {}
    normalization_dict["min_coords"] = min_coords
    normalization_dict["max_coords"] = max_coords
    normalization_dict["image_res"] = image_res


    # coordinates = np.round(points[:, :2] / max_coordinates[None,:2] * image_res[None])
    coordinates = \
        np.round(
            (ps[:, :2] - min_coords[None, :2]) / (max_coords[None,:2] - min_coords[None, :2]) * image_res[None])
    coordinates = np.minimum(np.maximum(coordinates, np.zeros_like(image_res)),
                                image_res - 1)

    density = np.zeros((height, width), dtype=np.float32)

    unique_coordinates, counts = np.unique(coordinates, return_counts=True, axis=0)
    # print(np.unique(counts))
    # counts = np.minimum(counts, 1e2)

    unique_coordinates = unique_coordinates.astype(np.int32)

    density[unique_coordinates[:, 1], unique_coordinates[:, 0]] = counts
    density = density / np.max(density)

    mask_mid = (density >= 0.001) & (density <= 0.04)
    mask_high = density > 0.04
    density[mask_mid] *= 25
    density[mask_high] = 1.0

    return density, normalization_dict


def enhance_point_cloud_continuity(point_cloud, enhancement_method='interpolation', **kwargs):
    """
    增强点云连续性以改善墙体边缘的连续性

    参数:
        point_cloud: 输入点云 (N, 3)
        enhancement_method: 增强方法 ('interpolation', 'gaussian_noise', 'combined')
        **kwargs: 方法特定参数

    返回:
        增强后的点云
    """
    if len(point_cloud) == 0:
        return point_cloud

    if enhancement_method == 'interpolation':
        return _linear_interpolation_enhancement(point_cloud, **kwargs)
    elif enhancement_method == 'gaussian_noise':
        return _gaussian_noise_enhancement(point_cloud, **kwargs)
    elif enhancement_method == 'combined':
        # 先插值再加噪声
        enhanced = _linear_interpolation_enhancement(point_cloud, **kwargs)
        return _gaussian_noise_enhancement(enhanced, noise_samples=2, noise_std=0.3)
    else:
        return point_cloud


def _linear_interpolation_enhancement(point_cloud, max_distance=2.0, max_interpolations=3):
    """线性插值增强点云密度"""
    if len(point_cloud) < 2:
        return point_cloud

    enhanced_points = list(point_cloud)

    # 按Z坐标排序以保持层次结构
    z_indices = np.argsort(point_cloud[:, 2])
    sorted_points = point_cloud[z_indices]

    # 在相邻点之间插值
    for i in range(len(sorted_points) - 1):
        p1, p2 = sorted_points[i], sorted_points[i + 1]
        distance = np.linalg.norm(p2 - p1)

        if distance > max_distance:
            num_interpolations = min(int(distance / 1.0), max_interpolations)

            for j in range(1, num_interpolations + 1):
                t = j / (num_interpolations + 1)
                interp_point = p1 + t * (p2 - p1)
                enhanced_points.append(interp_point)

    return np.array(enhanced_points)


def _gaussian_noise_enhancement(point_cloud, noise_samples=3, noise_std=0.5):
    """高斯噪声增强点云密度"""
    enhanced_points = list(point_cloud)

    for point in point_cloud:
        for _ in range(noise_samples):
            # Z方向噪声较小以保持层次结构
            noise = np.random.normal(0, [noise_std, noise_std, noise_std * 0.3], 3)
            noisy_point = point + noise
            enhanced_points.append(noisy_point)

    return np.array(enhanced_points)


def apply_morphological_operations(density_map, operation='closing', kernel_size=3, iterations=1):
    """
    对密度图应用形态学操作以改善边缘连续性

    参数:
        density_map: 输入密度图
        operation: 操作类型 ('closing', 'dilation', 'opening', 'erosion')
        kernel_size: 核大小
        iterations: 迭代次数

    返回:
        处理后的密度图
    """
    # 转换为uint8进行形态学操作
    density_uint8 = (density_map * 255).astype(np.uint8)

    # 创建结构元素
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))

    if operation == 'closing':
        processed = cv2.morphologyEx(density_uint8, cv2.MORPH_CLOSE, kernel, iterations=iterations)
    elif operation == 'dilation':
        processed = cv2.dilate(density_uint8, kernel, iterations=iterations)
    elif operation == 'opening':
        processed = cv2.morphologyEx(density_uint8, cv2.MORPH_OPEN, kernel, iterations=iterations)
    elif operation == 'erosion':
        processed = cv2.erode(density_uint8, kernel, iterations=iterations)
    else:
        processed = density_uint8

    # 转换回float32并归一化
    return processed.astype(np.float32) / 255.0


def enhance_wall_visibility(density_map, method='gradient', **kwargs):
    """
    增强墙体可见性

    参数:
        density_map: 输入密度图
        method: 增强方法 ('gradient', 'adaptive_threshold', 'edge_detection')
        **kwargs: 方法特定参数

    返回:
        增强后的密度图
    """
    if method == 'gradient':
        return _gradient_enhancement(density_map, **kwargs)
    elif method == 'adaptive_threshold':
        return _adaptive_threshold_enhancement(density_map, **kwargs)
    elif method == 'edge_detection':
        return _edge_detection_enhancement(density_map, **kwargs)
    else:
        return density_map


def _gradient_enhancement(density_map, alpha=0.3):
    """基于梯度的墙体增强"""
    # 计算梯度
    grad_x = cv2.Sobel(density_map, cv2.CV_32F, 1, 0, ksize=3)
    grad_y = cv2.Sobel(density_map, cv2.CV_32F, 0, 1, ksize=3)
    gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)

    # 归一化梯度
    if np.max(gradient_magnitude) > 0:
        gradient_magnitude = gradient_magnitude / np.max(gradient_magnitude)

    # 将梯度信息融合到原始密度图
    enhanced = density_map + alpha * gradient_magnitude
    return np.clip(enhanced, 0, 1)


def _adaptive_threshold_enhancement(density_map, block_size=11, C=2):
    """自适应阈值增强"""
    density_uint8 = (density_map * 255).astype(np.uint8)

    # 应用自适应阈值
    adaptive_thresh = cv2.adaptiveThreshold(
        density_uint8, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
        cv2.THRESH_BINARY, block_size, C
    )

    # 与原始图像结合
    enhanced = np.maximum(density_map, adaptive_thresh.astype(np.float32) / 255.0)
    return enhanced


def _edge_detection_enhancement(density_map, low_threshold=50, high_threshold=150):
    """基于Canny边缘检测的增强"""
    density_uint8 = (density_map * 255).astype(np.uint8)

    # Canny边缘检测
    edges = cv2.Canny(density_uint8, low_threshold, high_threshold)

    # 将边缘信息融合到原始密度图
    enhanced = np.maximum(density_map, edges.astype(np.float32) / 255.0)
    return enhanced


def generate_density_enhanced(point_cloud, width=set_img_size, height=set_img_size,
                            enable_continuity_enhancement=True,
                            enable_morphological_ops=True,
                            enable_wall_enhancement=True,
                            continuity_method='interpolation',
                            morphological_operation='closing',
                            wall_enhancement_method='gradient',
                            **kwargs):
    """
    增强版点云密度投影生成函数

    主要改进:
    1. 点云连续性增强 - 解决稀疏点云分布问题
    2. 形态学后处理 - 改善墙体边缘连续性
    3. 墙体可见性增强 - 使墙体更加突出
    4. 保持与原始函数的兼容性

    参数:
        point_cloud: 输入点云 (N, 3)
        width, height: 输出图像尺寸
        enable_continuity_enhancement: 是否启用连续性增强
        enable_morphological_ops: 是否启用形态学操作
        enable_wall_enhancement: 是否启用墙体增强
        continuity_method: 连续性增强方法
        morphological_operation: 形态学操作类型
        wall_enhancement_method: 墙体增强方法
        **kwargs: 其他参数

    返回:
        增强后的密度图和归一化字典
    """

    # 步骤1: 点云连续性增强
    if enable_continuity_enhancement and len(point_cloud) > 0:
        enhanced_point_cloud = enhance_point_cloud_continuity(
            point_cloud, enhancement_method=continuity_method, **kwargs
        )
    else:
        enhanced_point_cloud = point_cloud

    # 步骤2: 使用增强后的点云生成基础密度图（复用原始逻辑）
    ps = enhanced_point_cloud * -1
    ps[:,0] *= -1
    ps[:,1] *= -1

    image_res = np.array((width, height))

    max_coords = np.max(ps, axis=0)
    min_coords = np.min(ps, axis=0)
    max_m_min = max_coords - min_coords

    max_coords = max_coords + 0.1 * max_m_min
    min_coords = min_coords - 0.1 * max_m_min

    normalization_dict = {}
    normalization_dict["min_coords"] = min_coords
    normalization_dict["max_coords"] = max_coords
    normalization_dict["image_res"] = image_res

    # 坐标归一化和投影
    coordinates = \
        np.round(
            (ps[:, :2] - min_coords[None, :2]) / (max_coords[None,:2] - min_coords[None, :2]) * image_res[None])
    coordinates = np.minimum(np.maximum(coordinates, np.zeros_like(image_res)),
                                image_res - 1)

    density = np.zeros((height, width), dtype=np.float32)

    unique_coordinates, counts = np.unique(coordinates, return_counts=True, axis=0)
    unique_coordinates = unique_coordinates.astype(np.int32)

    density[unique_coordinates[:, 1], unique_coordinates[:, 0]] = counts

    # 改进的归一化 - 避免除零错误
    max_density = np.max(density)
    if max_density > 0:
        density = density / max_density

    # 原始非线性映射
    mask_mid = (density >= 0.001) & (density <= 0.04)
    mask_high = density > 0.04
    density[mask_mid] *= 25
    density[mask_high] = 1.0

    # 步骤3: 形态学后处理
    if enable_morphological_ops:
        kernel_size = kwargs.get('morphological_kernel_size', 3)
        iterations = kwargs.get('morphological_iterations', 1)
        density = apply_morphological_operations(
            density, operation=morphological_operation,
            kernel_size=kernel_size, iterations=iterations
        )

    # 步骤4: 墙体可见性增强
    if enable_wall_enhancement:
        density = enhance_wall_visibility(
            density, method=wall_enhancement_method, **kwargs
        )

    # 最终归一化确保值在[0,1]范围内
    density = np.clip(density, 0, 1)

    return density, normalization_dict


def normalize_point(point, normalization_dict):

    min_coords = normalization_dict["min_coords"]
    max_coords = normalization_dict["max_coords"]
    image_res = normalization_dict["image_res"]

    point_2d = \
        np.round(
            (point[:2] - min_coords[:2]) / (max_coords[:2] - min_coords[:2]) * image_res)
    point_2d = np.minimum(np.maximum(point_2d, np.zeros_like(image_res)),
                            image_res - 1)

    point[:2] = point_2d.tolist()

    return point

def normalize_annotations(scene_path, normalization_dict):
    annotation_path = os.path.join(scene_path, "annotation_3d.json")
    with open(annotation_path, "r") as f:
        annotation_json = json.load(f)

    for line in annotation_json["lines"]:
        point = line["point"]
        point = normalize_point(point, normalization_dict)
        line["point"] = point

    for junction in annotation_json["junctions"]:
        point = junction["coordinate"]
        point = normalize_point(point, normalization_dict)
        junction["coordinate"] = point

    return annotation_json

def parse_floor_plan_polys(annos):
    planes = []
    for semantic in annos['semantics']:
        for planeID in semantic['planeID']:
            if annos['planes'][planeID]['type'] == 'floor':
                planes.append({'planeID': planeID, 'type': semantic['type']})

        if semantic['type'] == 'outwall':
            outerwall_planes = semantic['planeID']

    # extract hole vertices
    lines_holes = []
    for semantic in annos['semantics']:
        if semantic['type'] in ['window', 'door']:
            for planeID in semantic['planeID']:
                lines_holes.extend(np.where(np.array(annos['planeLineMatrix'][planeID]))[0].tolist())
    lines_holes = np.unique(lines_holes)

    # junctions on the floor
    junctions = np.array([junc['coordinate'] for junc in annos['junctions']])
    junction_floor = np.where(np.isclose(junctions[:, -1], 0))[0]

    # construct each polygon
    polygons = []
    for plane in planes:
        lineIDs = np.where(np.array(annos['planeLineMatrix'][plane['planeID']]))[0].tolist()
        junction_pairs = [np.where(np.array(annos['lineJunctionMatrix'][lineID]))[0].tolist() for lineID in lineIDs]
        polygon = convert_lines_to_vertices(junction_pairs)
        polygons.append([polygon[0], plane['type']])

    # outerwall_floor = []
    # for planeID in outerwall_planes:
    #     lineIDs = np.where(np.array(annos['planeLineMatrix'][planeID]))[0].tolist()
    #     lineIDs = np.setdiff1d(lineIDs, lines_holes)
    #     junction_pairs = [np.where(np.array(annos['lineJunctionMatrix'][lineID]))[0].tolist() for lineID in lineIDs]
    #     for start, end in junction_pairs:
    #         if start in junction_floor and end in junction_floor:
    #             outerwall_floor.append([start, end])

    # outerwall_polygon = convert_lines_to_vertices(outerwall_floor)
    # polygons.append([outerwall_polygon[0], 'outwall'])

    return polygons

def convert_lines_to_vertices(lines):
    """
    convert line representation to polygon vertices

    """
    polygons = []
    lines = np.array(lines)

    polygon = None
    while len(lines) != 0:
        if polygon is None:
            polygon = lines[0].tolist()
            lines = np.delete(lines, 0, 0)

        lineID, juncID = np.where(lines == polygon[-1])
        vertex = lines[lineID[0], 1 - juncID[0]]
        lines = np.delete(lines, lineID, 0)

        if vertex in polygon:
            polygons.append(polygon)
            polygon = None
        else:
            polygon.append(vertex)

    return polygons



def generate_coco_dict(annos, polygons, curr_instance_id, curr_img_id, ignore_types):

    junctions = np.array([junc['coordinate'][:2] for junc in annos['junctions']])

    coco_annotation_dict_list = []

    for poly_ind, (polygon, poly_type) in enumerate(polygons):
        if poly_type in ignore_types:
            continue

        polygon = junctions[np.array(polygon)]

        poly_shapely = Polygon(polygon)
        area = poly_shapely.area

        # assert area > 10
        # if area < 100:
        if poly_type not in ['door', 'window'] and area < 100:
            continue
        if poly_type in ['door', 'window'] and area < 1:
            continue
        
        rectangle_shapely = poly_shapely.envelope

        ### here we convert door/window annotation into a single line
        if poly_type in ['door', 'window']:
            assert polygon.shape[0] == 4
            midp_1 = (polygon[0] + polygon[1])/2
            midp_2 = (polygon[1] + polygon[2])/2
            midp_3 = (polygon[2] + polygon[3])/2
            midp_4 = (polygon[3] + polygon[0])/2

            dist_1_3 = np.square(midp_1 -midp_3).sum()
            dist_2_4 = np.square(midp_2 -midp_4).sum()
            if dist_1_3 > dist_2_4:
                polygon = np.row_stack([midp_1, midp_3])
            else:
                polygon = np.row_stack([midp_2, midp_4])

        coco_seg_poly = []
        poly_sorted = resort_corners(polygon)

        for p in poly_sorted:
            coco_seg_poly += list(p)

        # Slightly wider bounding box
        bound_pad = 2
        bb_x, bb_y = rectangle_shapely.exterior.xy
        bb_x = np.unique(bb_x)
        bb_y = np.unique(bb_y)
        bb_x_min = np.maximum(np.min(bb_x) - bound_pad, 0)
        bb_y_min = np.maximum(np.min(bb_y) - bound_pad, 0)

        bb_x_max = np.minimum(np.max(bb_x) + bound_pad, set_img_size - 1)
        bb_y_max = np.minimum(np.max(bb_y) + bound_pad, set_img_size - 1)

        bb_width = (bb_x_max - bb_x_min)
        bb_height = (bb_y_max - bb_y_min)

        coco_bb = [bb_x_min, bb_y_min, bb_width, bb_height]

        coco_annotation_dict = {
                "segmentation": [coco_seg_poly],
                "area": area,
                "iscrowd": 0,
                "image_id": curr_img_id,
                "bbox": coco_bb,
                "category_id": type2id[poly_type],
                "id": curr_instance_id}
        
        coco_annotation_dict_list.append(coco_annotation_dict)
        curr_instance_id += 1


    return coco_annotation_dict_list