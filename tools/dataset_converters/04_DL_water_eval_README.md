# 04_DL_water_eval.py 使用说明

## 概述

`04_DL_water_eval.py` 是一个用于评估04_toyuhui_DL_water数据集实例分割结果的脚本。该脚本实现了以下主要功能：

1. **坐标转换**：将04_toyuhui_DL_water中的实例掩码从点云坐标系转换到预处理结果的坐标系（1024x1024）
2. **空洞填充**：使用凸包填充和形态学操作解决坐标转换后的掩码空洞问题
3. **可视化生成**：创建RGB赋色的实例掩码图像，便于区分不同实例
4. **真值掩码生成**：直接从floorplan.json生成真值掩码，确保坐标系一致性
5. **精度评估**：计算实例分割的精度指标（Precision、Recall、F1-Score等）
6. **分数据集处理**：分别处理训练集、验证集和测试集

## 主要特性

### 坐标转换流程（已修正）

**更新后的water_mapping.txt格式**：
```
第一行：点云坐标偏置量 offset_x, offset_y, offset_z
第二行：点云最小坐标 minP[0], minP[1], minP[2]
第三行：图像分辨率 map_resolution
```

1. **从water坐标到原始点云坐标**：
   ```
   cloud_x = pixel_i * map_resolution + minP[0] + offset_x
   cloud_y = pixel_j * map_resolution + minP[1] + offset_y
   ```
   **关键修正**：C++代码在投影前先对点云减去了坐标偏置量，所以逆转换时需要加回偏置量。

2. **从原始点云坐标到预处理坐标**：
   - 应用旋转变换（与generate_coco_hc_0722.py中process_single_las_file完全相同）
   - 使用原始建筑坐标范围（来自calculate_slopes）进行归一化
   - 归一化到图像坐标系（1024x1024）

3. **坐标系一致性保证**：
   - 预测掩码和真值掩码都使用calculate_slopes返回的原始建筑坐标范围
   - 通过坐标偏置修正确保两者在同一坐标系下对齐

4. **关键参数说明**：
   - `offset_coords`：点云坐标偏置量，用于修正C++投影时的坐标偏移
   - `minP`：去噪后点云的最小坐标
   - `map_resolution`：像素到米的转换比例

4. **空洞填充处理**：
   - 使用自适应核大小的形态学操作填充
   - 避免凸包计算失败问题
   - 确保转换后的实例掩码连续完整

### 输出文件

#### 转换后的掩码文件（保存到 `/home/<USER>/data/RS10_data/04_toyuhui_DL_water_trans/`）
- `transformed_mask.png`：转换后的实例掩码（灰度图）
- `transformed_rgb.png`：转换后的实例掩码（RGB彩色图）
- `{scene_name}_gt_mask.png`：真值掩码（灰度图）
- `{scene_name}_gt_rgb.png`：真值掩码（RGB彩色图）

#### 评估结果文件（保存到 `output/04_DL_water/`）
- `0821_train_set/evaluation_results.json`：训练集评估结果
- `0821_val_set/evaluation_results.json`：验证集评估结果
- `0821_test_set/evaluation_results.json`：测试集评估结果
- `per_scene_results.json`：每个场景的详细结果

## 使用方法

### 基本用法

```bash
cd /home/<USER>/repos/Mask2Former_v2
python tools/dataset_converters/04_DL_water_eval.py
```

### 完整参数

```bash
python tools/dataset_converters/04_DL_water_eval.py \
    --water_data_root /home/<USER>/data/RS10_data/04_toyuhui_DL_water \
    --original_data_root /home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/ \
    --filelists_dir /home/<USER>/data/RS10_data/00_dataset_spilt/filelists \
    --coco_ann_file /home/<USER>/data/RS10_data/04_to_yuhui_densitymap/0814_All_1024x1024/annotations/train.json \
    --output_root output/04_DL_water \
    --trans_output_root /home/<USER>/data/RS10_data/04_toyuhui_DL_water_trans \
    --mask_type final \
    --image_size 1024
```

### 测试用法

#### 测试单个场景（推荐用于验证新数据格式）
```bash
# 测试更新后的样例数据
python tools/dataset_converters/04_DL_water_eval.py \
    --test_scene huaqiaocheng_res_uf_RS10_11 \
    --mask_type final
```

#### 限制场景数量
```bash
python tools/dataset_converters/04_DL_water_eval.py --max_scenes 5
```

## 参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--water_data_root` | `/home/<USER>/data/RS10_data/04_toyuhui_DL_water` | 04_toyuhui_DL_water数据路径 |
| `--original_data_root` | `/home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/` | 原始RS10数据路径 |
| `--filelists_dir` | `/home/<USER>/data/RS10_data/00_dataset_spilt/filelists` | 文件列表目录 |
| `--coco_ann_file` | `/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0722_256x256/annotations/instances_train.json` | COCO标注文件路径 |
| `--output_root` | `output/04_DL_water` | 输出根目录 |
| `--trans_output_root` | `/home/<USER>/data/RS10_data/04_toyuhui_DL_water_trans` | 转换后掩码保存路径 |
| `--mask_type` | `final` | 掩码类型：final 或 watershedGray |
| `--image_size` | `1024` | 图像尺寸 |
| `--max_scenes` | `None` | 最大处理场景数量（用于测试） |
| `--test_scene` | `None` | 测试单个场景（如：huaqiaocheng_res_uf_RS10_11） |

## 评估指标说明

### 主要指标

- **Precision@85**：IoU阈值为0.85时的精确率
- **Recall@85**：IoU阈值为0.85时的召回率
- **F1@85**：IoU阈值为0.85时的F1分数
- **mAP_85**：IoU阈值为0.85时的平均精确率

### 指标类型

- **微平均指标（Micro-averaged）**：基于总体TP/FP/FN计算
- **宏平均指标（Macro-averaged）**：每张图像指标的平均值

## 输出示例

### 控制台输出

```
🚀 开始04_DL_water评估
  Water数据路径: /home/<USER>/data/RS10_data/04_toyuhui_DL_water
  原始数据路径: /home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/
  输出路径: output/04_DL_water
  转换掩码保存路径: /home/<USER>/data/RS10_data/04_toyuhui_DL_water_trans

📂 加载文件列表...
  训练集: 108 场景
  验证集: 11 场景
  测试集: 37 场景
  可用场景: 74 个

==================================================
🔄 处理 TRAIN 集
==================================================
  📊 可处理场景: 53
  
🔄 处理场景: scene_name
  📊 原始掩码尺寸: (height, width)
  📊 实例数量: N
  💾 保存RGB可视化: path/transformed_rgb.png
  ✅ 转换完成，保存到: output_dir

📊 train 评估结果:
  场景数量: 5 (有效: 5)
  微平均指标 (Overall):
    Precision: 0.0000
    Recall: 0.0000
    F1-Score: 0.0000
  宏平均指标 (Per-scene):
    Precision: 0.0000
    Recall: 0.0000
    F1-Score: 0.0000
  统计信息:
    Total TP: 0, FP: 35, FN: 7
    Total Pred: 35, GT: 7
```

### JSON结果文件

```json
{
  "split": "train",
  "num_scenes": 5,
  "num_valid_scenes": 5,
  "precision_micro@85": 0.0,
  "recall_micro@85": 0.0,
  "f1_micro@85": 0.0,
  "precision_macro@85": 0.0,
  "recall_macro@85": 0.0,
  "f1_macro@85": 0.0,
  "mAP_85": 0.0,
  "total_tp": 0,
  "total_fp": 35,
  "total_fn": 7,
  "total_pred_instances": 35,
  "total_gt_instances": 7,
  "per_scene_results": {
    "scene_name": {
      "tp": 0,
      "fp": 8,
      "fn": 1,
      "precision": 0.0,
      "recall": 0.0,
      "f1": 0.0,
      "num_pred": 8,
      "num_gt": 1
    }
  }
}
```

## 注意事项

1. **依赖项**：脚本会自动检测pycocotools是否可用，如果不可用会使用备用实现
2. **数据路径**：确保所有数据路径正确，特别是floorplan.json文件的位置
3. **内存使用**：处理大量场景时可能需要较多内存
4. **坐标转换精度**：转换过程中会进行四舍五入，可能影响精度

## 故障排除

### 常见错误

1. **找不到floorplan.json**：检查original_data_root路径是否正确
2. **找不到COCO标注文件**：检查coco_ann_file路径是否正确
3. **内存不足**：使用--max_scenes参数限制处理数量

### 调试建议

1. 先使用`--max_scenes 1`测试单个场景
2. 检查生成的可视化文件确认坐标转换是否正确
3. 查看控制台输出了解处理进度和错误信息

## 调试和分析工具

由于坐标转换存在复杂的偏置问题，我们提供了专门的分析工具：

### 📋 分析文档
- `tools/docs/04_DL_water_coordinate_analysis.md` - 坐标转换问题的深度分析文档

### 🔧 调试工具
- `tools/debug/coordinate_parameter_analyzer.py` - 深入分析所有坐标参数
- `tools/debug/single_point_tracker.py` - 追踪单个点的完整转换过程

### 使用方法
```bash
# 分析坐标参数
python tools/debug/coordinate_parameter_analyzer.py

# 追踪点转换过程
python tools/debug/single_point_tracker.py
```

## 更新日志

- **2024-08-21**：初始版本，支持坐标转换和精度评估
  - 支持训练集、验证集、测试集分别处理
  - 支持RGB可视化和真值掩码生成
  - 支持IoU@85精度评估
  - **重大修正**：解决坐标转换问题
    - **根本原因发现**：water_mapping.txt缺少点云坐标偏置信息
    - **更新数据格式**：支持新的water_mapping.txt格式（包含坐标偏置量）
    - **修正转换逻辑**：正确处理C++投影时的坐标偏置
    - **新增掩码类型支持**：支持final.png和watershedGray.png两种掩码
    - **输出路径优化**：根据掩码类型分别保存结果到不同文件夹
  - 简化空洞填充，使用自适应形态学操作替代易失败的凸包计算
  - 修正图像分辨率为1024x1024
  - 改进COCO标注文件自动检测，支持hc_rs10_q2_wo_floor_2_0804_1024x1024格式
  - 保持真值掩码生成逻辑不变（已确认正确）
