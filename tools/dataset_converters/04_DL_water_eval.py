#!/usr/bin/env python3
"""
04_DL_water_eval.py

实现对/home/<USER>/data/RS10_data/04_toyuhui_DL_water路径下实例掩码的读取和坐标转换，
然后计算实例分割的精度指标并按规定格式输出。

主要功能：
1. 读取04_toyuhui_DL_water中的实例掩码和转换参数
2. 将坐标从点云坐标系转换到预处理结果的坐标系
3. 保存转换后的实例掩码图像和真值掩码
4. 计算实例分割精度指标并分别处理训练集、验证集和测试集

坐标转换流程：
1. water坐标 -> 点云坐标：cloud_x = pixel_i * map_resolution + minP[0]
2. 点云坐标 -> 预处理坐标：应用旋转变换 + 归一化到图像坐标系
"""

import argparse
import json
import os
import sys
import numpy as np
import cv2
from tqdm import tqdm
import tempfile

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

try:
    from pycocotools.coco import COCO
    from pycocotools import mask as mask_util
    from pycocotools.cocoeval import COCOeval
    PYCOCOTOOLS_AVAILABLE = True
except ImportError:
    print("⚠️  pycocotools not available, using fallback implementation")
    PYCOCOTOOLS_AVAILABLE = False

# 导入必要的工具函数
try:
    from tools.dataset_converters.hc_utils import calculate_slopes, rot_normalize_vectorized_gt
except ImportError as e:
    print(f"导入hc_utils失败: {e}")
    # 尝试直接导入
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    try:
        from hc_utils import calculate_slopes, rot_normalize_vectorized_gt
    except ImportError as e2:
        print(f"直接导入hc_utils也失败: {e2}")
        # 如果都失败了，我们需要自己实现这些函数
        print("将使用简化的实现...")

        def calculate_slopes(vectorized_gt, type='mean'):
            """简化的斜率计算实现"""
            slopes_vec = []
            all_x, all_y = [], []

            for frame in vectorized_gt['frames']:
                for instance in frame['instances']:
                    for edge in instance['edge']:
                        vertices_x = np.array(edge['vertices']['x'], dtype=np.float32)
                        vertices_y = np.array(edge['vertices']['y'], dtype=np.float32)
                        ps = np.column_stack((vertices_x, vertices_y))
                        all_x.extend(edge['vertices']['x'])
                        all_y.extend(edge['vertices']['y'])

                        # 计算相邻点的欧式距离
                        distances = np.sqrt(np.sum((ps[:-1] - ps[1:])**2, axis=1))

                        # 找到最长的间隔点对
                        max_distance_index = np.argmax(distances)
                        if ps[max_distance_index + 1, 0] != ps[max_distance_index, 0]:
                            slopes = (ps[max_distance_index + 1, 1] - ps[max_distance_index, 1]) / \
                                (ps[max_distance_index + 1, 0] - ps[max_distance_index, 0])
                            slopes_vec.append(slopes)

            if not slopes_vec:
                return 0.0, np.array([0.0, 0.0]), np.array([0.0, 0.0])

            # 计算平均斜率和角度
            mean_slope = np.mean(slopes_vec)
            slope_angle = np.arctan(mean_slope)

            # 计算坐标范围
            max_coords = np.array([max(all_x), max(all_y)])
            min_coords = np.array([min(all_x), min(all_y)])

            return slope_angle, max_coords, min_coords

# 类别映射
type2id = {'living room': 0, 'kitchen': 1, 'bedroom': 2, 'bathroom': 3, 'balcony': 4, 'corridor': 5,
           'dining room': 6, 'study': 7, 'studio': 8, 'store room': 9, 'garden': 10, 'laundry room': 11,
           'office': 12, 'basement': 13, 'garage': 14, 'undefined': 15, 'door': 16, 'window': 17}

id2type = {v: k for k, v in type2id.items()}

def load_water_mapping(mapping_file):
    """
    加载water_mapping.txt文件中的转换参数

    更新后的文件格式：
    第一行：点云坐标偏置量 offset_x, offset_y, offset_z
    第二行：点云最小坐标 minP[0], minP[1], minP[2]
    第三行：图像分辨率 map_resolution
    """
    with open(mapping_file, 'r') as f:
        lines = f.readlines()

    # 第一行：点云坐标偏置量
    offset_coords = list(map(float, lines[0].strip().split()))
    # 第二行：点云最小坐标
    min_coords = list(map(float, lines[1].strip().split()))
    # 第三行：图像分辨率
    map_resolution = float(lines[2].strip())

    return offset_coords, min_coords, map_resolution

def load_instance_mask(mask_file):
    """加载实例掩码图像"""
    mask = cv2.imread(mask_file, cv2.IMREAD_GRAYSCALE)
    if mask is None:
        raise ValueError(f"无法加载掩码文件: {mask_file}")
    return mask

def water_coords_to_pointcloud_coords(pixel_coords, offset_coords, min_coords_water, map_resolution):
    """
    将04_toyuhui_DL_water中的像素坐标转换回原始点云坐标

    根据更新后的理解：
    1. C++代码在投影前先对点云减去了坐标偏置量
    2. 投影公式：int i = int((cloud - offset - minP[0]) / map_resolution)

    逆转换：
    cloud_x = pixel_i * map_resolution + minP[0] + offset_x
    cloud_y = pixel_j * map_resolution + minP[1] + offset_y
    """
    if pixel_coords.shape[0] == 0:
        return np.empty((0, 2), dtype=np.float32)

    cloud_coords = np.zeros_like(pixel_coords, dtype=np.float32)
    # pixel_coords[:, 0] 是图像中的x坐标（列），对应点云的x
    # pixel_coords[:, 1] 是图像中的y坐标（行），对应点云的y
    cloud_coords[:, 0] = pixel_coords[:, 0] * map_resolution + min_coords_water[0] + offset_coords[0]
    cloud_coords[:, 1] = pixel_coords[:, 1] * map_resolution + min_coords_water[1] + offset_coords[1]
    return cloud_coords

def pointcloud_coords_to_preprocessed_coords(cloud_coords, slope_angle, max_coords, min_coords, image_res):
    """
    将原始点云坐标转换到预处理脚本的坐标系

    关键理解：
    1. 04_toyuhui_DL_water使用的是原始点云坐标（未旋转）
    2. generate_coco_hc_0722.py中先旋转点云，再生成密度图
    3. 所以我们需要：原始点云坐标 → 旋转 → 归一化到图像坐标

    这里的max_coords和min_coords是旋转后的坐标范围！
    """
    if cloud_coords.shape[0] == 0:
        return np.empty((0, 2), dtype=np.int32)

    # 步骤1：应用旋转变换（与process_single_las_file中完全相同）
    rotation_matrix = cv2.getRotationMatrix2D((0, 0), np.degrees(slope_angle), 1)
    c = float(rotation_matrix[0, 0])
    s = float(rotation_matrix[0, 1])

    x = cloud_coords[:, 0].astype(np.float32)
    y = cloud_coords[:, 1].astype(np.float32)
    xr = x * c + y * s
    yr = -x * s + y * c
    rotated_coords = np.column_stack((xr, yr))

    # 步骤2：归一化到图像坐标系（与generate_density中完全相同）
    # 注意：这里的min_coords和max_coords应该是旋转后坐标的范围
    normalized_coords = np.round(
        (rotated_coords - min_coords) / (max_coords - min_coords) * image_res
    )

    # 限制在图像边界内
    normalized_coords = np.minimum(np.maximum(normalized_coords, np.zeros_like(image_res)),
                                   image_res - 1).astype(np.int32)

    return normalized_coords

def extract_instance_pixels(instance_mask):
    """从实例掩码中提取每个实例的像素坐标"""
    instances = {}
    unique_values = np.unique(instance_mask)
    
    for value in unique_values:
        if value == 0:  # 跳过背景
            continue
        
        # 获取该实例的像素坐标
        y_coords, x_coords = np.where(instance_mask == value)
        pixel_coords = np.column_stack((x_coords, y_coords))
        instances[int(value)] = pixel_coords
    
    return instances

def create_transformed_mask(instances_transformed, image_shape):
    """
    根据转换后的实例像素坐标创建新的掩码图像
    使用凸包填充来解决空洞问题
    """
    transformed_mask = np.zeros(image_shape, dtype=np.uint8)

    for instance_id, pixel_coords in instances_transformed.items():
        # 确保坐标在图像边界内
        valid_coords = pixel_coords[
            (pixel_coords[:, 0] >= 0) & (pixel_coords[:, 0] < image_shape[1]) &
            (pixel_coords[:, 1] >= 0) & (pixel_coords[:, 1] < image_shape[0])
        ]

        if len(valid_coords) < 3:  # 至少需要3个点才能形成多边形
            # 如果点太少，直接标记像素
            if len(valid_coords) > 0:
                transformed_mask[valid_coords[:, 1], valid_coords[:, 0]] = instance_id
            continue

        # 直接使用简单填充方法，避免凸包计算问题
        # 先标记原始像素
        transformed_mask[valid_coords[:, 1], valid_coords[:, 0]] = instance_id

        # 使用形态学操作填充空洞
        instance_mask = (transformed_mask == instance_id).astype(np.uint8)

        # 使用更大的核进行填充，确保连续性
        kernel_size = max(3, min(7, len(valid_coords) // 10))  # 根据点数自适应核大小
        kernel = np.ones((kernel_size, kernel_size), np.uint8)

        # 先进行闭运算填充小空洞
        filled_mask = cv2.morphologyEx(instance_mask, cv2.MORPH_CLOSE, kernel)

        # 再进行开运算去除噪声
        kernel_small = np.ones((3, 3), np.uint8)
        filled_mask = cv2.morphologyEx(filled_mask, cv2.MORPH_OPEN, kernel_small)

        # 更新掩码
        transformed_mask[filled_mask > 0] = instance_id

    return transformed_mask

def create_rgb_visualization(instance_mask, output_path):
    """
    创建RGB赋色的实例掩码可视化
    参考tools/inference_with_evaluators.py中的save_clean_visualization
    """
    # 定义颜色方案（参考simplified_projection_processor）
    colors = [
        (255, 0, 0),    # 红色
        (0, 255, 0),    # 绿色
        (0, 0, 255),    # 蓝色
        (255, 255, 0),  # 黄色
        (255, 0, 255),  # 品红
        (0, 255, 255),  # 青色
        (128, 0, 128),  # 紫色
        (255, 165, 0),  # 橙色
        (255, 20, 147), # 深粉色
        (0, 191, 255),  # 深天蓝色
        (154, 205, 50), # 黄绿色
        (255, 69, 0),   # 红橙色
        (138, 43, 226), # 蓝紫色
        (255, 215, 0),  # 金色
        (50, 205, 50),  # 酸橙绿
        (255, 105, 180),# 热粉色
    ]
    
    # 创建白色背景的RGB图像
    rgb_image = np.full((instance_mask.shape[0], instance_mask.shape[1], 3), 255, dtype=np.uint8)
    
    # 获取所有实例ID
    unique_ids = np.unique(instance_mask)
    
    # 为每个实例直接赋色（不混合，直接覆盖）
    for i, instance_id in enumerate(unique_ids):
        if instance_id == 0:  # 跳过背景
            continue
        
        color = colors[(i-1) % len(colors)]  # i-1因为跳过了背景
        rgb_image[instance_mask == instance_id] = color
    
    # 保存结果
    cv2.imwrite(output_path, rgb_image)
    print(f"  💾 保存RGB可视化: {output_path}")

def load_scene_metadata(data_root, scene_name):
    """加载场景的元数据信息"""
    # 标准路径格式：{data_root}/{scene_name}/Annotations/floorplan.json
    floorplan_path = os.path.join(data_root, scene_name, 'Annotations/floorplan.json')

    if not os.path.exists(floorplan_path):
        raise FileNotFoundError(f"找不到floorplan.json: {floorplan_path}")

    with open(floorplan_path, 'r') as f:
        vectorized_gt = json.load(f)

    # 计算斜率角度和坐标范围（与generate_coco_hc_0722.py完全相同）
    slope_angle, max_coords, min_coords = calculate_slopes(vectorized_gt, type='mean')
    pointcloud_roi = vectorized_gt['frames'][0]['boundingBox']

    return {
        'slope_angle': slope_angle,
        'max_coords': max_coords,
        'min_coords': min_coords,
        'pointcloud_roi': pointcloud_roi,
        'vectorized_gt': vectorized_gt
    }

def create_gt_mask_from_floorplan(vectorized_gt, slope_angle, max_coords, min_coords, image_size=1024):
    """
    从floorplan.json直接创建真值掩码

    关键修正：需要先计算旋转后的坐标范围，然后用于归一化
    这样才能与generate_density中的处理保持一致
    """
    image_res = np.array([image_size, image_size])
    gt_mask = np.zeros((image_size, image_size), dtype=np.uint8)

    # 步骤1：收集所有原始坐标并计算旋转后的范围
    all_rotated_coords = []
    rotation_matrix = cv2.getRotationMatrix2D((0, 0), np.degrees(slope_angle), 1)
    c = float(rotation_matrix[0, 0])
    s = float(rotation_matrix[0, 1])

    for frame in vectorized_gt['frames']:
        for instance in frame['instances']:
            if instance['category'] != 'room':
                continue

            for edge in instance['edge']:
                vertices_x = np.array(edge['vertices']['x'], dtype=np.float32)
                vertices_y = np.array(edge['vertices']['y'], dtype=np.float32)

                # 应用旋转变换（与process_single_las_file相同）
                xr = vertices_x * c + vertices_y * s
                yr = -vertices_x * s + vertices_y * c
                rotated_coords = np.column_stack((xr, yr))
                all_rotated_coords.append(rotated_coords)

    if not all_rotated_coords:
        return gt_mask

    # 计算旋转后坐标的范围
    all_rotated_coords = np.vstack(all_rotated_coords)
    rotated_max_coords = np.max(all_rotated_coords, axis=0)
    rotated_min_coords = np.min(all_rotated_coords, axis=0)

    # 添加边距（与calculate_slopes中相同）
    max_m_min = rotated_max_coords - rotated_min_coords
    rotated_max_coords = rotated_max_coords + 0.1 * max_m_min
    rotated_min_coords = rotated_min_coords - 0.1 * max_m_min

    print(f"  📊 旋转后坐标范围: max=({rotated_max_coords[0]:.2f}, {rotated_max_coords[1]:.2f}), min=({rotated_min_coords[0]:.2f}, {rotated_min_coords[1]:.2f})")

    # 步骤2：使用旋转后的坐标范围进行归一化和填充
    instance_id = 1
    for frame in vectorized_gt['frames']:
        for instance in frame['instances']:
            if instance['category'] != 'room':
                continue

            for edge in instance['edge']:
                vertices_x = np.array(edge['vertices']['x'], dtype=np.float32)
                vertices_y = np.array(edge['vertices']['y'], dtype=np.float32)

                # 应用旋转变换
                xr = vertices_x * c + vertices_y * s
                yr = -vertices_x * s + vertices_y * c
                rotated_ps = np.column_stack((xr, yr))

                # 使用旋转后的坐标范围进行归一化
                coordinates = np.round(
                    (rotated_ps - rotated_min_coords) / (rotated_max_coords - rotated_min_coords) * image_res
                )
                coordinates = np.minimum(np.maximum(coordinates, np.zeros_like(image_res)),
                                       image_res - 1).astype(np.int32)

                # 填充多边形
                if len(coordinates) >= 3:
                    cv2.fillPoly(gt_mask, [coordinates], instance_id)

            instance_id += 1

    return gt_mask, rotated_max_coords, rotated_min_coords

def create_gt_mask_from_floorplan_simple(vectorized_gt, slope_angle, max_coords, min_coords, image_size=1024):
    """
    从floorplan.json直接创建真值掩码（简化版本）
    直接使用原始建筑坐标范围，与预测掩码保持一致
    """
    image_res = np.array([image_size, image_size])
    gt_mask = np.zeros((image_size, image_size), dtype=np.uint8)

    rotation_matrix = cv2.getRotationMatrix2D((0, 0), np.degrees(slope_angle), 1)
    c = float(rotation_matrix[0, 0])
    s = float(rotation_matrix[0, 1])

    instance_id = 1
    for frame in vectorized_gt['frames']:
        for instance in frame['instances']:
            if instance['category'] != 'room':
                continue

            for edge in instance['edge']:
                vertices_x = np.array(edge['vertices']['x'], dtype=np.float32)
                vertices_y = np.array(edge['vertices']['y'], dtype=np.float32)

                # 应用旋转变换（与预测掩码相同）
                xr = vertices_x * c + vertices_y * s
                yr = -vertices_x * s + vertices_y * c
                rotated_ps = np.column_stack((xr, yr))

                # 使用原始建筑坐标范围进行归一化（与预测掩码相同）
                coordinates = np.round(
                    (rotated_ps - min_coords) / (max_coords - min_coords) * image_res
                )
                coordinates = np.minimum(np.maximum(coordinates, np.zeros_like(image_res)),
                                       image_res - 1).astype(np.int32)

                # 填充多边形
                if len(coordinates) >= 3:
                    cv2.fillPoly(gt_mask, [coordinates], instance_id)

            instance_id += 1

    return gt_mask

def load_file_lists(filelists_dir):
    """加载训练集、验证集和测试集的文件列表"""
    def read_list(fname):
        if not os.path.exists(fname):
            return set()
        with open(fname, 'r') as f:
            return set([l.strip() for l in f if l.strip()])
    
    train_scenes = read_list(os.path.join(filelists_dir, 'train.txt'))
    val_scenes = read_list(os.path.join(filelists_dir, 'val.txt'))
    test_scenes = read_list(os.path.join(filelists_dir, 'test.txt'))
    
    return train_scenes, val_scenes, test_scenes

def process_single_scene(scene_name, water_data_root, original_data_root, output_root, mask_type='final', image_size=1024):
    """
    处理单个场景的坐标转换

    Args:
        mask_type: 'final' 或 'watershedGray'，选择处理哪种掩码文件
    """
    print(f"\n🔄 处理场景: {scene_name} (掩码类型: {mask_type})")

    # 检查必要文件是否存在
    water_scene_dir = os.path.join(water_data_root, scene_name)
    mask_file = os.path.join(water_scene_dir, f'{mask_type}.png')
    mapping_file = os.path.join(water_scene_dir, 'water_mapping.txt')

    if not os.path.exists(mask_file):
        print(f"  ❌ 跳过 {scene_name}: 找不到{mask_type}.png")
        return None

    if not os.path.exists(mapping_file):
        print(f"  ❌ 跳过 {scene_name}: 找不到water_mapping.txt")
        return None

    try:
        # 1. 加载water数据
        instance_mask = load_instance_mask(mask_file)
        offset_coords, min_coords_water, map_resolution = load_water_mapping(mapping_file)

        print(f"  📊 原始掩码尺寸: {instance_mask.shape}")
        print(f"  📊 实例数量: {len(np.unique(instance_mask)) - 1}")  # -1排除背景
        print(f"  📊 坐标偏置: ({offset_coords[0]:.2f}, {offset_coords[1]:.2f}, {offset_coords[2]:.2f})")
        print(f"  📊 Water参数: minP=({min_coords_water[0]:.2f}, {min_coords_water[1]:.2f}), resolution={map_resolution:.4f}")

        # 2. 加载场景元数据
        metadata = load_scene_metadata(original_data_root, scene_name)
        print(f"  📊 预处理参数: slope_angle={np.degrees(metadata['slope_angle']):.2f}°")
        print(f"  📊 坐标范围: max=({metadata['max_coords'][0]:.2f}, {metadata['max_coords'][1]:.2f}), min=({metadata['min_coords'][0]:.2f}, {metadata['min_coords'][1]:.2f})")

        # 2.1 显示坐标信息
        print(f"  📊 坐标偏置: ({offset_coords[0]:.2f}, {offset_coords[1]:.2f}, {offset_coords[2]:.2f})")

        # 3. 提取实例像素坐标
        instances = extract_instance_pixels(instance_mask)

        # 4. 坐标转换
        instances_transformed = {}
        image_res = np.array([image_size, image_size])

        for instance_id, pixel_coords in instances.items():
            # 步骤1：water坐标 -> 原始点云坐标（包含偏置修正）
            cloud_coords = water_coords_to_pointcloud_coords(
                pixel_coords, offset_coords, min_coords_water, map_resolution
            )

            # 步骤2：点云坐标 -> 预处理坐标
            preprocessed_coords = pointcloud_coords_to_preprocessed_coords(
                cloud_coords, metadata['slope_angle'],
                metadata['max_coords'], metadata['min_coords'], image_res
            )

            instances_transformed[instance_id] = preprocessed_coords

        # 5. 创建转换后的掩码
        transformed_mask = create_transformed_mask(instances_transformed, (image_size, image_size))

        # 6. 从floorplan.json创建真值掩码（保持原有正确的实现）
        gt_mask = create_gt_mask_from_floorplan_simple(
            metadata['vectorized_gt'], metadata['slope_angle'],
            metadata['max_coords'], metadata['min_coords'], image_size
        )

        print(f"  📊 真值掩码使用原始坐标范围: max=({metadata['max_coords'][0]:.2f}, {metadata['max_coords'][1]:.2f}), min=({metadata['min_coords'][0]:.2f}, {metadata['min_coords'][1]:.2f})")

        # 调试：打印第一个实例的坐标转换过程
        if instances:
            first_instance_id = list(instances.keys())[0]
            first_pixel_coords = instances[first_instance_id]
            if len(first_pixel_coords) > 0:
                print(f"  🔍 实例{first_instance_id}坐标转换调试:")
                print(f"    原始water像素: ({first_pixel_coords[0][0]}, {first_pixel_coords[0][1]})")

                # 计算转换后的点云坐标
                cloud_x = first_pixel_coords[0][0] * map_resolution + min_coords_water[0] + offset_coords[0]
                cloud_y = first_pixel_coords[0][1] * map_resolution + min_coords_water[1] + offset_coords[1]
                print(f"    原始点云坐标: ({cloud_x:.2f}, {cloud_y:.2f})")

                # 找到对应的转换结果
                if first_instance_id in instances_transformed:
                    final_coords = instances_transformed[first_instance_id][0]
                    print(f"    最终结果: ({final_coords[0]}, {final_coords[1]})")

        # 7. 保存结果
        output_scene_dir = os.path.join(output_root, scene_name)
        os.makedirs(output_scene_dir, exist_ok=True)

        # 保存转换后的掩码（包含掩码类型标识）
        mask_output_path = os.path.join(output_scene_dir, f'transformed_mask_{mask_type}.png')
        cv2.imwrite(mask_output_path, transformed_mask)

        # 保存RGB可视化（包含掩码类型标识）
        rgb_output_path = os.path.join(output_scene_dir, f'transformed_rgb_{mask_type}.png')
        create_rgb_visualization(transformed_mask, rgb_output_path)

        # 保存真值掩码
        gt_mask_path = os.path.join(output_scene_dir, f'{scene_name}_gt_mask.png')
        cv2.imwrite(gt_mask_path, gt_mask)

        # 保存真值RGB可视化
        gt_rgb_path = os.path.join(output_scene_dir, f'{scene_name}_gt_rgb.png')
        create_rgb_visualization(gt_mask, gt_rgb_path)

        print(f"  ✅ 转换完成，保存到: {output_scene_dir}")
        print(f"  📊 转换后实例数量: {len(np.unique(transformed_mask)) - 1}")
        print(f"  📊 真值实例数量: {len(np.unique(gt_mask)) - 1}")

        return {
            'scene_name': scene_name,
            'transformed_mask': transformed_mask,
            'gt_mask': gt_mask,
            'metadata': metadata,
            'output_dir': output_scene_dir
        }

    except Exception as e:
        print(f"  ❌ 处理 {scene_name} 时出错: {e}")
        print(f"  📍 错误详情:")
        print(f"    Water场景目录: {water_scene_dir}")
        print(f"    掩码文件: {mask_file} ({'存在' if os.path.exists(mask_file) else '不存在'})")
        print(f"    映射文件: {mapping_file} ({'存在' if os.path.exists(mapping_file) else '不存在'})")

        floorplan_path = os.path.join(original_data_root, scene_name, 'Annotations/floorplan.json')
        print(f"    Floorplan文件: {floorplan_path} ({'存在' if os.path.exists(floorplan_path) else '不存在'})")

        import traceback
        traceback.print_exc()
        return None

def polygon_to_mask(polygon, height, width):
    """将多边形转换为掩码（使用OpenCV，不依赖matplotlib）"""
    mask = np.zeros((height, width), dtype=np.uint8)

    # 重新整理多边形坐标
    if isinstance(polygon, list) and len(polygon) > 0:
        if isinstance(polygon[0], list):
            # 多个多边形
            for poly in polygon:
                poly_coords = np.array(poly).reshape(-1, 2).astype(np.int32)
                if len(poly_coords) >= 3:
                    cv2.fillPoly(mask, [poly_coords], 1)
        else:
            # 单个多边形
            poly_coords = np.array(polygon).reshape(-1, 2).astype(np.int32)
            if len(poly_coords) >= 3:
                cv2.fillPoly(mask, [poly_coords], 1)

    return mask

def load_gt_masks_from_coco(coco_ann_file, scene_name, image_size=256):
    """从COCO标注文件中加载真值掩码"""
    try:
        if PYCOCOTOOLS_AVAILABLE:
            return load_gt_masks_from_coco_pycocotools(coco_ann_file, scene_name, image_size)
        else:
            return load_gt_masks_from_coco_fallback(coco_ann_file, scene_name, image_size)
    except Exception as e:
        print(f"  ❌ 加载真值掩码时出错: {e}")
        return None

def load_gt_masks_from_coco_pycocotools(coco_ann_file, scene_name, image_size=256):
    """使用pycocotools加载真值掩码"""
    coco = COCO(coco_ann_file)

    # 查找对应的图像
    img_ids = coco.getImgIds()
    target_img_id = None

    for img_id in img_ids:
        img_info = coco.imgs[img_id]
        if img_info['file_name'].replace('.png', '') == scene_name:
            target_img_id = img_id
            break

    if target_img_id is None:
        print(f"  ⚠️  在COCO标注中找不到场景: {scene_name}")
        return None

    # 获取该图像的所有标注
    ann_ids = coco.getAnnIds(imgIds=[target_img_id])
    anns = coco.loadAnns(ann_ids)

    # 创建真值掩码
    gt_mask = np.zeros((image_size, image_size), dtype=np.uint8)
    gt_masks_by_category = {}

    for i, ann in enumerate(anns):
        # 将多边形转换为掩码
        if isinstance(ann['segmentation'], list):
            # 多边形格式
            rle = mask_util.frPyObjects(ann['segmentation'], image_size, image_size)
            mask = mask_util.decode(rle)
            if len(mask.shape) == 3:
                mask = mask.sum(axis=2) > 0
        else:
            # RLE格式
            mask = mask_util.decode(ann['segmentation'])

        # 为每个实例分配唯一ID
        instance_id = i + 1
        gt_mask[mask > 0] = instance_id

        # 按类别存储
        category_id = ann['category_id']
        if category_id not in gt_masks_by_category:
            gt_masks_by_category[category_id] = []
        gt_masks_by_category[category_id].append({
            'mask': mask,
            'instance_id': instance_id,
            'area': ann.get('area', np.sum(mask))
        })

    return {
        'gt_mask': gt_mask,
        'gt_masks_by_category': gt_masks_by_category,
        'annotations': anns
    }

def load_gt_masks_from_coco_fallback(coco_ann_file, scene_name, image_size=256):
    """不使用pycocotools的备用方案"""
    with open(coco_ann_file, 'r') as f:
        coco_data = json.load(f)

    # 查找对应的图像
    target_img_id = None
    for img in coco_data['images']:
        if img['file_name'].replace('.png', '') == scene_name:
            target_img_id = img['id']
            break

    if target_img_id is None:
        print(f"  ⚠️  在COCO标注中找不到场景: {scene_name}")
        return None

    # 获取该图像的所有标注
    anns = [ann for ann in coco_data['annotations'] if ann['image_id'] == target_img_id]

    # 创建真值掩码
    gt_mask = np.zeros((image_size, image_size), dtype=np.uint8)
    gt_masks_by_category = {}

    for i, ann in enumerate(anns):
        # 将多边形转换为掩码
        if isinstance(ann['segmentation'], list):
            # 多边形格式
            mask = polygon_to_mask(ann['segmentation'], image_size, image_size)
        else:
            print(f"  ⚠️  跳过RLE格式的标注（需要pycocotools）")
            continue

        # 为每个实例分配唯一ID
        instance_id = i + 1
        gt_mask[mask > 0] = instance_id

        # 按类别存储
        category_id = ann['category_id']
        if category_id not in gt_masks_by_category:
            gt_masks_by_category[category_id] = []
        gt_masks_by_category[category_id].append({
            'mask': mask,
            'instance_id': instance_id,
            'area': ann.get('area', np.sum(mask))
        })

    return {
        'gt_mask': gt_mask,
        'gt_masks_by_category': gt_masks_by_category,
        'annotations': anns
    }

def save_gt_visualization(scene_result, gt_data, output_dir):
    """保存真值掩码的可视化"""
    if gt_data is None:
        return

    gt_mask = gt_data['gt_mask']
    scene_name = scene_result['scene_name']

    # 保存真值掩码
    gt_mask_path = os.path.join(output_dir, f'{scene_name}_gt_mask.png')
    cv2.imwrite(gt_mask_path, gt_mask)

    # 保存真值RGB可视化
    gt_rgb_path = os.path.join(output_dir, f'{scene_name}_gt_rgb.png')
    create_rgb_visualization(gt_mask, gt_rgb_path)

    print(f"  💾 保存真值可视化: {gt_rgb_path}")

def compute_iou(mask1, mask2):
    """计算两个掩码的IoU"""
    intersection = np.logical_and(mask1, mask2).sum()
    union = np.logical_or(mask1, mask2).sum()

    if union == 0:
        return 0.0

    return intersection / union

def evaluate_scene_metrics(pred_mask, gt_data, iou_threshold=0.85):
    """计算单个场景的评估指标"""
    if gt_data is None:
        return {
            'tp': 0, 'fp': 0, 'fn': 0,
            'precision': 0.0, 'recall': 0.0, 'f1': 0.0,
            'num_pred': 0, 'num_gt': 0
        }

    gt_mask = gt_data['gt_mask']

    # 提取预测实例
    pred_instances = extract_instance_pixels(pred_mask)
    gt_instances = extract_instance_pixels(gt_mask)

    num_pred = len(pred_instances)
    num_gt = len(gt_instances)

    if num_pred == 0 and num_gt == 0:
        return {
            'tp': 0, 'fp': 0, 'fn': 0,
            'precision': 1.0, 'recall': 1.0, 'f1': 1.0,
            'num_pred': 0, 'num_gt': 0
        }

    # 创建IoU矩阵
    iou_matrix = np.zeros((num_pred, num_gt))

    pred_masks = []
    gt_masks = []

    # 为每个预测实例创建二值掩码
    for pred_pixels in pred_instances.values():
        pred_binary_mask = np.zeros_like(pred_mask, dtype=bool)
        pred_binary_mask[pred_pixels[:, 1], pred_pixels[:, 0]] = True
        pred_masks.append(pred_binary_mask)

    # 为每个真值实例创建二值掩码
    for gt_pixels in gt_instances.values():
        gt_binary_mask = np.zeros_like(gt_mask, dtype=bool)
        gt_binary_mask[gt_pixels[:, 1], gt_pixels[:, 0]] = True
        gt_masks.append(gt_binary_mask)

    # 计算IoU矩阵
    for i, pred_binary_mask in enumerate(pred_masks):
        for j, gt_binary_mask in enumerate(gt_masks):
            iou_matrix[i, j] = compute_iou(pred_binary_mask, gt_binary_mask)

    # 使用贪心匹配计算TP, FP, FN
    tp = 0
    matched_gt = set()
    matched_pred = set()

    # 按IoU从高到低排序进行匹配
    matches = []
    for i in range(num_pred):
        for j in range(num_gt):
            if iou_matrix[i, j] >= iou_threshold:
                matches.append((iou_matrix[i, j], i, j))

    matches.sort(reverse=True)  # 按IoU降序排列

    for _, pred_idx, gt_idx in matches:
        if pred_idx not in matched_pred and gt_idx not in matched_gt:
            tp += 1
            matched_pred.add(pred_idx)
            matched_gt.add(gt_idx)

    fp = num_pred - tp
    fn = num_gt - tp

    # 计算精度、召回率和F1分数
    precision = tp / num_pred if num_pred > 0 else 0.0
    recall = tp / num_gt if num_gt > 0 else 0.0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0

    return {
        'tp': tp, 'fp': fp, 'fn': fn,
        'precision': precision, 'recall': recall, 'f1': f1,
        'num_pred': num_pred, 'num_gt': num_gt
        # 'iou_matrix': iou_matrix  # 移除numpy数组以避免JSON序列化错误
    }

def save_evaluation_results(results, output_dir, split_name):
    """保存评估结果到文件"""
    os.makedirs(output_dir, exist_ok=True)

    # 计算总体指标
    total_tp = sum(r['tp'] for r in results.values())
    total_fp = sum(r['fp'] for r in results.values())
    total_fn = sum(r['fn'] for r in results.values())
    total_pred = sum(r['num_pred'] for r in results.values())
    total_gt = sum(r['num_gt'] for r in results.values())

    # 微平均指标（基于总体TP/FP/FN）
    overall_precision = total_tp / total_pred if total_pred > 0 else 0.0
    overall_recall = total_tp / total_gt if total_gt > 0 else 0.0
    overall_f1 = 2 * overall_precision * overall_recall / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0.0

    # 宏平均指标（每张图像的平均）
    valid_results = [r for r in results.values() if r['num_pred'] > 0 or r['num_gt'] > 0]
    if valid_results:
        macro_precision = np.mean([r['precision'] for r in valid_results])
        macro_recall = np.mean([r['recall'] for r in valid_results])
        macro_f1 = np.mean([r['f1'] for r in valid_results])
    else:
        macro_precision = macro_recall = macro_f1 = 0.0

    # 准备结果字典
    evaluation_results = {
        'split': split_name,
        'num_scenes': len(results),
        'num_valid_scenes': len(valid_results),

        # 微平均指标（基于总体TP/FP/FN）
        'precision_micro@85': overall_precision,
        'recall_micro@85': overall_recall,
        'f1_micro@85': overall_f1,

        # 宏平均指标（每张图像的平均）
        'precision_macro@85': macro_precision,
        'recall_macro@85': macro_recall,
        'f1_macro@85': macro_f1,

        # 兼容性指标
        'mAP_85': macro_precision,
        'avg_precision': macro_precision,
        'avg_recall': macro_recall,
        'avg_f1_score': macro_f1,
        'overall_precision': overall_precision,
        'overall_recall': overall_recall,
        'overall_f1_score': overall_f1,

        # 统计信息
        'total_tp': total_tp,
        'total_fp': total_fp,
        'total_fn': total_fn,
        'total_pred_instances': total_pred,
        'total_gt_instances': total_gt,

        # 每个场景的详细结果
        'per_scene_results': results
    }

    # 保存主要结果
    results_file = os.path.join(output_dir, 'evaluation_results.json')
    with open(results_file, 'w') as f:
        json.dump(evaluation_results, f, indent=2)

    # 保存每个场景的详细结果
    per_scene_file = os.path.join(output_dir, 'per_scene_results.json')
    with open(per_scene_file, 'w') as f:
        json.dump(results, f, indent=2)

    # 打印结果摘要
    print(f"\n📊 {split_name} 评估结果:")
    print(f"  场景数量: {len(results)} (有效: {len(valid_results)})")
    print(f"  微平均指标 (Overall):")
    print(f"    Precision: {overall_precision:.4f}")
    print(f"    Recall: {overall_recall:.4f}")
    print(f"    F1-Score: {overall_f1:.4f}")
    print(f"  宏平均指标 (Per-scene):")
    print(f"    Precision: {macro_precision:.4f}")
    print(f"    Recall: {macro_recall:.4f}")
    print(f"    F1-Score: {macro_f1:.4f}")
    print(f"  统计信息:")
    print(f"    Total TP: {total_tp}, FP: {total_fp}, FN: {total_fn}")
    print(f"    Total Pred: {total_pred}, GT: {total_gt}")
    print(f"  📁 结果保存到: {output_dir}")

    return evaluation_results

def find_data_paths():
    """自动查找数据路径"""
    print("🔍 自动查找数据路径...")

    # 可能的根路径
    possible_roots = [
        '/home/<USER>/data/RS10_data',
        '/home/<USER>/data',
        '/data/RS10_data',
        '/mnt/data/RS10_data'
    ]

    paths = {
        'water_data_root': None,
        'filelists_dir': None,
        'coco_ann_file': None,
        'original_data_root': '/home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/'
    }

    for root in possible_roots:
        if not os.path.exists(root):
            continue

        print(f"  📁 检查根路径: {root}")

        # 查找04_toyuhui_DL_water
        water_candidates = [
            os.path.join(root, '04_toyuhui_DL_water'),
            os.path.join(root, '04_to_yuhui_DL_water'),
        ]
        for candidate in water_candidates:
            if os.path.exists(candidate):
                paths['water_data_root'] = candidate
                print(f"    ✅ 找到water数据: {candidate}")
                break

        # 查找filelists
        filelist_candidates = [
            os.path.join(root, '00_dataset_spilt/filelists'),
            os.path.join(root, 'filelists'),
        ]
        for candidate in filelist_candidates:
            if os.path.exists(candidate):
                paths['filelists_dir'] = candidate
                print(f"    ✅ 找到文件列表: {candidate}")
                break

        # 查找COCO标注文件
        try:
            items = os.listdir(root)
            for item in items:
                # 查找包含hc_rs10和1024的目录，或者包含0804的目录
                if ('hc_rs10' in item and '1024' in item) or ('hc_rs10' in item and '0804' in item):
                    ann_dir = os.path.join(root, item, 'annotations')
                    if os.path.exists(ann_dir):
                        ann_files = os.listdir(ann_dir)
                        # 优先查找train.json，然后是instances_train.json
                        for ann_file in ['train.json', 'instances_train.json']:
                            if ann_file in ann_files:
                                paths['coco_ann_file'] = os.path.join(ann_dir, ann_file)
                                print(f"    ✅ 找到COCO标注: {paths['coco_ann_file']}")
                                break
                        if paths['coco_ann_file']:
                            break
        except Exception as e:
            print(f"    ⚠️  搜索COCO标注时出错: {e}")

    return paths

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='04_DL_water评估脚本')

    # 自动查找路径
    auto_paths = find_data_paths()

    parser.add_argument('--water_data_root',
                       default=auto_paths.get('water_data_root', '/home/<USER>/data/RS10_data/04_toyuhui_DL_water'),
                       help='04_toyuhui_DL_water数据路径')
    parser.add_argument('--original_data_root',
                       default=auto_paths.get('original_data_root', '/home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/'),
                       help='原始RS10数据路径')
    parser.add_argument('--filelists_dir',
                       default=auto_paths.get('filelists_dir', '/home/<USER>/data/RS10_data/00_dataset_spilt/filelists'),
                       help='文件列表目录')
    parser.add_argument('--coco_ann_file',
                       default=auto_paths.get('coco_ann_file', '/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0804_1024x1024/annotations/instances_train.json'),
                       help='COCO标注文件路径')
    parser.add_argument('--output_root',
                       default='output/04_DL_water',
                       help='输出根目录')
    parser.add_argument('--trans_output_root',
                       default='/home/<USER>/data/RS10_data/04_toyuhui_DL_water_trans',
                       help='转换后掩码保存路径')
    parser.add_argument('--mask_type',
                       choices=['final', 'watershedGray'],
                       default='final',
                       help='掩码类型：final 或 watershedGray')
    parser.add_argument('--image_size', type=int, default=1024,
                       help='图像尺寸')
    parser.add_argument('--max_scenes', type=int, default=None,
                       help='最大处理场景数量（用于测试）')
    parser.add_argument('--test_scene', type=str, default=None,
                       help='测试单个场景（如：huaqiaocheng_res_uf_RS10_11）')

    args = parser.parse_args()

    print("🚀 开始04_DL_water评估")
    print(f"  Water数据路径: {args.water_data_root}")
    print(f"  原始数据路径: {args.original_data_root}")
    print(f"  文件列表路径: {args.filelists_dir}")
    print(f"  COCO标注文件: {args.coco_ann_file}")
    print(f"  输出路径: {args.output_root}")
    print(f"  转换掩码保存路径: {args.trans_output_root}")
    print(f"  掩码类型: {args.mask_type}")
    if args.test_scene:
        print(f"  🧪 测试模式: 只处理场景 {args.test_scene}")

    # 验证关键路径
    print("\n🔍 验证路径...")
    critical_paths = {
        'Water数据路径': args.water_data_root,
        '原始数据路径': args.original_data_root,
        '文件列表路径': args.filelists_dir,
    }

    missing_paths = []
    for name, path in critical_paths.items():
        if os.path.exists(path):
            print(f"  ✅ {name}: {path}")
        else:
            print(f"  ❌ {name}: {path} (不存在)")
            missing_paths.append(name)

    # COCO标注文件单独检查
    if os.path.exists(args.coco_ann_file):
        print(f"  ✅ COCO标注文件: {args.coco_ann_file}")
    else:
        print(f"  ⚠️  COCO标注文件: {args.coco_ann_file} (不存在，将跳过COCO评估)")

    if missing_paths:
        print(f"\n❌ 关键路径缺失: {', '.join(missing_paths)}")
        print("请检查路径配置或使用命令行参数指定正确路径")
        return

    # 创建输出目录
    os.makedirs(args.output_root, exist_ok=True)
    os.makedirs(args.trans_output_root, exist_ok=True)

    # 检查是否为测试单个场景模式
    if args.test_scene:
        print(f"\n🧪 测试模式：只处理场景 {args.test_scene}")

        # 检查场景是否存在
        scene_dir = os.path.join(args.water_data_root, args.test_scene)
        if not os.path.exists(scene_dir):
            print(f"❌ 测试场景不存在: {scene_dir}")
            return

        # 处理单个测试场景
        result = process_single_scene(
            args.test_scene, args.water_data_root, args.original_data_root,
            args.trans_output_root, args.mask_type, args.image_size
        )

        if result:
            print(f"\n✅ 测试场景 {args.test_scene} 处理成功")
            print(f"  转换后实例数量: {len(np.unique(result['transformed_mask'])) - 1}")
            print(f"  真值实例数量: {len(np.unique(result['gt_mask'])) - 1}")

            # 计算简单的评估指标
            gt_data = {'gt_mask': result['gt_mask']}
            metrics = evaluate_scene_metrics(result['transformed_mask'], gt_data)

            print(f"  评估指标:")
            print(f"    Precision: {metrics['precision']:.4f}")
            print(f"    Recall: {metrics['recall']:.4f}")
            print(f"    F1-Score: {metrics['f1_score']:.4f}")
        else:
            print(f"❌ 测试场景 {args.test_scene} 处理失败")

        return

    # 正常模式：处理所有场景
    # 加载文件列表
    print("\n📂 加载文件列表...")
    train_scenes, val_scenes, test_scenes = load_file_lists(args.filelists_dir)
    print(f"  训练集: {len(train_scenes)} 场景")
    print(f"  验证集: {len(val_scenes)} 场景")
    print(f"  测试集: {len(test_scenes)} 场景")

    # 获取所有可用的场景
    available_scenes = set()
    if os.path.exists(args.water_data_root):
        available_scenes = set([d for d in os.listdir(args.water_data_root)
                               if os.path.isdir(os.path.join(args.water_data_root, d))])

    print(f"  可用场景: {len(available_scenes)} 个")

    # 处理每个数据集分割
    splits = {
        'train': train_scenes,
        'val': val_scenes,
        'test': test_scenes
    }

    output_dirs = {
        'train': os.path.join(args.output_root, f'0821_train_set_{args.mask_type}'),
        'val': os.path.join(args.output_root, f'0821_val_set_{args.mask_type}'),
        'test': os.path.join(args.output_root, f'0821_test_set_{args.mask_type}')
    }

    for split_name, scene_list in splits.items():
        if not scene_list:
            print(f"\n⚠️  跳过 {split_name}: 没有场景")
            continue

        print(f"\n{'='*50}")
        print(f"🔄 处理 {split_name.upper()} 集")
        print(f"{'='*50}")

        # 找到该分割中可用的场景
        available_split_scenes = scene_list.intersection(available_scenes)
        missing_scenes = scene_list - available_scenes

        if missing_scenes:
            print(f"  ⚠️  缺失场景 ({len(missing_scenes)}): {list(missing_scenes)[:5]}{'...' if len(missing_scenes) > 5 else ''}")

        if not available_split_scenes:
            print(f"  ❌ {split_name} 集没有可用场景")
            continue

        print(f"  📊 可处理场景: {len(available_split_scenes)}")

        # 限制处理数量（用于测试）
        if args.max_scenes:
            available_split_scenes = list(available_split_scenes)[:args.max_scenes]
            print(f"  🔧 限制处理数量: {len(available_split_scenes)}")

        # 处理场景
        scene_results = {}
        evaluation_results = {}

        for scene_name in tqdm(available_split_scenes, desc=f"处理{split_name}场景"):
            # 坐标转换
            result = process_single_scene(
                scene_name, args.water_data_root, args.original_data_root,
                args.trans_output_root, args.mask_type, args.image_size
            )

            if result is None:
                continue

            scene_results[scene_name] = result

            # 计算评估指标（直接使用从floorplan.json生成的真值掩码）
            gt_data = {'gt_mask': result['gt_mask']}
            metrics = evaluate_scene_metrics(result['transformed_mask'], gt_data)
            evaluation_results[scene_name] = metrics

        # 保存评估结果
        if evaluation_results:
            save_evaluation_results(evaluation_results, output_dirs[split_name], split_name)
        else:
            print(f"  ❌ {split_name} 集没有有效的评估结果")

    print(f"\n🎉 评估完成！")
    print(f"  转换后的掩码保存在: {args.trans_output_root}")
    print(f"  评估结果保存在: {args.output_root}")

# 移除了analyze_coordinate_bias函数，因为坐标偏置问题已通过更新water_mapping.txt解决

if __name__ == '__main__':
    main()
